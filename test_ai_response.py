#!/usr/bin/env python3
"""
Test script to verify AI assistant response without Rasa server
"""

import os
import sys
import django
from django.conf import settings

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'eaglora.settings')
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    django.setup()
    
    from django.contrib.auth.models import User
    from ai_assistant.rasa_integration import RasaIntegration
    
    def test_ai_response():
        """Test AI response generation"""
        print("Testing AI Assistant Response Generation")
        print("=" * 50)
        
        # Create or get a test user
        try:
            user = User.objects.get(username='admin')
        except User.DoesNotExist:
            user = User.objects.create_user(
                username='admin',
                email='<EMAIL>',
                password='admin123'
            )
            print("Created test user: admin")
        
        # Initialize Rasa integration
        rasa_integration = RasaIntegration()
        
        # Test messages
        test_messages = [
            "get admin profile",
            "show john doe information",
            "check my leave balance",
            "view attendance",
        ]
        
        for message in test_messages:
            print(f"\nTesting message: '{message}'")
            print("-" * 30)
            
            try:
                response = rasa_integration.send_message(
                    message=message,
                    sender_id="test_session",
                    user=user
                )
                
                print(f"Action: {response.get('action')}")
                print(f"Message: {response.get('message')}")
                print(f"URL: {response.get('url')}")
                print(f"Filters: {response.get('filters')}")
                print(f"Confidence: {response.get('confidence')}")
                print(f"Intent: {response.get('intent')}")
                
                if response.get('action') == 'error':
                    print("❌ ERROR RESPONSE DETECTED")
                else:
                    print("✅ SUCCESS RESPONSE")
                    
            except Exception as e:
                print(f"❌ EXCEPTION: {str(e)}")
                import traceback
                traceback.print_exc()
    
    if __name__ == "__main__":
        test_ai_response()
        
except Exception as e:
    print(f"Failed to setup Django: {e}")
    print("This script needs to be run from the Django project directory with proper environment setup.")
