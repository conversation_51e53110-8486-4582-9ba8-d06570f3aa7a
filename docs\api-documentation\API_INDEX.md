# Eaglora HRMS - Complete API Index

## 📋 Overview

This is the complete index of all API endpoints in the Eaglora HRMS system. Each endpoint is documented in detail with separate files for easy navigation and maintenance.

**Generated on:** January 2024
**Total Modules:** 9
**Total Endpoints:** 186
**Documentation Files:** 195+

## 🏗️ Complete API Structure

### 🔐 Authentication Module (`/api/auth/`)
| Endpoint | Method | Description | Documentation |
|----------|--------|-------------|---------------|
| `/login/` | POST | User authentication and JWT token generation | [auth/login.md](auth/login.md) |

### 👥 Employee Module (`/api/employee/`)
| Endpoint | Method | Description | Documentation |
|----------|--------|-------------|---------------|
| `/employees/` | GET | List all employees with filtering and pagination | [employee/employees-list.md](employee/employees-list.md) |
| `/employees/` | POST | Create new employee record | [employee/employee-create.md](employee/employee-create.md) |
| `/employees/{id}/` | GET | Get detailed employee information | [employee/employee-detail.md](employee/employee-detail.md) |
| `/employees/{id}/` | PUT | Update employee information | [employee/employee-update.md](employee/employee-update.md) |
| `/employees/{id}/` | DELETE | Delete employee record | [employee/employee-delete.md](employee/employee-delete.md) |
| `/employee-work-information/` | GET/POST/PUT | Manage employee work information | [employee/employee-work-info.md](employee/employee-work-info.md) |
| `/employee-bank-details/{id}/` | GET/PUT | Manage employee banking information | [employee/employee-bank-details.md](employee/employee-bank-details.md) |
| `/employee-type/` | GET/POST/PUT | Manage employee types and categories | [employee/employee-types.md](employee/employee-types.md) |
| `/list/employees/` | GET | Advanced employee search with pagination | [employee/employee-search.md](employee/employee-search.md) |
| `/employee-selector/` | GET | Hierarchy-based employee selection | [employee/employee-selector.md](employee/employee-selector.md) |
| `/employee-bulk-update/` | PUT | Bulk employee operations | [employee/employee-bulk-operations.md](employee/employee-bulk-operations.md) |

### ⏰ Attendance Module (`/api/attendance/`)
| Endpoint | Method | Description | Documentation |
|----------|--------|-------------|---------------|
| `/attendances/` | GET | List attendance records with filtering | [attendance/attendance-list.md](attendance/attendance-list.md) |
| `/attendances/` | POST | Create attendance record | [attendance/attendance-create.md](attendance/attendance-create.md) |
| `/clock-in/` | POST | Employee clock in operation | [attendance/clock-in.md](attendance/clock-in.md) |
| `/clock-out/` | POST | Employee clock out operation | [attendance/clock-out.md](attendance/clock-out.md) |
| `/attendance-requests/` | GET/POST/PUT | Manage attendance correction requests | [attendance/attendance-requests.md](attendance/attendance-requests.md) |
| `/overtime/` | GET/POST/PUT | Overtime tracking and management | [attendance/overtime-management.md](attendance/overtime-management.md) |

### 🏖️ Leave Module (`/api/leave/`)
| Endpoint | Method | Description | Documentation |
|----------|--------|-------------|---------------|
| `/leave-requests/` | GET/POST/PUT | Manage leave requests and applications | [leave/leave-requests.md](leave/leave-requests.md) |
| `/leave-types/` | GET/POST/PUT | Manage leave types and categories | [leave/leave-types.md](leave/leave-types.md) |
| `/leave-allocation/` | GET/POST/PUT | Manage employee leave allocations | [leave/leave-allocation.md](leave/leave-allocation.md) |
| `/holidays/` | GET/POST/PUT | Manage company holidays and calendar | [leave/holidays.md](leave/holidays.md) |

### 💰 Payroll Module (`/api/payroll/`)
| Endpoint | Method | Description | Documentation |
|----------|--------|-------------|---------------|
| `/contracts/` | GET/POST/PUT | Manage employee contracts | [payroll/contracts.md](payroll/contracts.md) |
| `/payslips/` | GET/POST | Generate and manage payslips | [payroll/payslips.md](payroll/payslips.md) |
| `/allowances/` | GET/POST/PUT | Manage salary allowances | [payroll/allowances.md](payroll/allowances.md) |
| `/deductions/` | GET/POST/PUT | Manage salary deductions | [payroll/deductions.md](payroll/deductions.md) |

### 🏢 Asset Module (`/api/asset/`)
| Endpoint | Method | Description | Documentation |
|----------|--------|-------------|---------------|
| `/assets/` | GET/POST/PUT | Manage company assets | [asset/assets.md](asset/assets.md) |
| `/asset-categories/` | GET/POST/PUT | Manage asset categories | [asset/asset-categories.md](asset/asset-categories.md) |
| `/asset-requests/` | GET/POST/PUT | Manage asset allocation requests | [asset/asset-requests.md](asset/asset-requests.md) |

### 🏗️ Base Module (`/api/base/`)
| Endpoint | Method | Description | Documentation |
|----------|--------|-------------|---------------|
| `/companies/` | GET/POST/PUT | Manage company information | [base/companies.md](base/companies.md) |
| `/departments/` | GET/POST/PUT | Manage departments | [base/departments.md](base/departments.md) |
| `/job-positions/` | GET/POST/PUT | Manage job positions | [base/job-positions.md](base/job-positions.md) |

### 🔔 Notifications Module (`/api/notifications/`)
| Endpoint | Method | Description | Documentation |
|----------|--------|-------------|---------------|
| `/notifications/` | GET | List user notifications | [notifications/notifications-list.md](notifications/notifications-list.md) |
| `/notifications/{id}/mark-read/` | POST | Mark notifications as read | [notifications/notification-actions.md](notifications/notification-actions.md) |

### 🤖 AI Assistant Module (`/ai-assistant/api/`)
| Endpoint | Method | Description | Documentation |
|----------|--------|-------------|---------------|
| `/message/` | POST | Send message to AI assistant | [ai-assistant/chat-message.md](ai-assistant/chat-message.md) |
| `/employee-search/` | GET | AI-powered employee search | [ai-assistant/employee-search.md](ai-assistant/employee-search.md) |

## 📊 API Statistics by Module

| Module | Endpoints | CRUD Operations | Special Features |
|--------|-----------|-----------------|------------------|
| Authentication | 1 | Login | JWT Token Management |
| Employee | 36 | Full CRUD | Search, Filter, Bulk Operations, Documents, Policies |
| Attendance | 25 | Full CRUD | Clock In/Out, GPS Validation, Overtime, Requests |
| Leave | 33 | Full CRUD | Approval Workflow, Allocations, Encashment, Holidays |
| Payroll | 25 | Full CRUD | PDF Generation, Contracts, Loans, Bonuses |
| Asset | 20 | Full CRUD | Allocation Workflow, Categories, Returns |
| Base | 30 | Full CRUD | Organizational Structure, Shifts, Work Types |
| Notifications | 6 | Full CRUD | Bulk Operations, Read Status Management |
| AI Assistant | 10 | Custom | Natural Language Processing, HRMS Integration |

## 🔧 Common Features Across All APIs

### 🔐 Authentication
- **JWT Token Required:** All endpoints (except login) require JWT authentication
- **Header Format:** `Authorization: Bearer YOUR_JWT_TOKEN`
- **Token Expiry:** 30 days (configurable)

### 📄 Pagination
Most list endpoints support pagination:
```json
{
  "count": 150,
  "next": "http://api.example.com/endpoint/?page=2",
  "previous": null,
  "results": [...]
}
```

### 🔍 Filtering & Search
Common filter parameters:
- `search` - General search term
- `page` - Page number
- `page_size` - Items per page
- `ordering` - Sort field
- Module-specific filters

### 📝 Response Formats

#### Success Response
```json
{
  "id": 1,
  "field1": "value1",
  "created_at": "2024-01-01T10:00:00Z"
}
```

#### Error Response
```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Error description",
    "details": {}
  }
}
```

## 🛡️ Security Features

- **JWT Authentication** - Secure token-based authentication
- **Permission-based Access** - Role-based access control
- **Input Validation** - Comprehensive data validation
- **Rate Limiting** - API abuse prevention
- **HTTPS Support** - Secure communication

## 📱 Integration Support

### Frontend Frameworks
- **React/Vue/Angular** - Complete examples provided
- **Mobile Apps** - React Native examples
- **Web Applications** - JavaScript/TypeScript support

### Backend Integration
- **Python** - Requests library examples
- **Node.js** - Fetch API examples
- **cURL** - Command-line examples

## 🚀 Quick Start Guide

### 1. Authentication
```bash
# Get JWT Token
curl -X POST "http://localhost:8000/api/auth/login/" \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin"}'
```

### 2. Use Token
```bash
# Use token in subsequent requests
curl -X GET "http://localhost:8000/api/employee/employees/" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 3. Explore APIs
- Browse module documentation: [README.md](README.md)
- Check specific endpoints using the links above
- Test with provided examples

## 📞 Support & Resources

### Documentation Structure
```
docs/api-documentation/
├── README.md                    # Main overview
├── API_INDEX.md                 # This complete index
├── generate_docs.py             # Documentation generator
├── auth/                        # Authentication APIs
├── employee/                    # Employee Management APIs
├── attendance/                  # Attendance Management APIs
├── leave/                       # Leave Management APIs
├── payroll/                     # Payroll Management APIs
├── asset/                       # Asset Management APIs
├── base/                        # Base Configuration APIs
├── notifications/               # Notification APIs
└── ai-assistant/               # AI Assistant APIs
```

### Getting Help
1. **Check specific endpoint documentation** - Each API has detailed docs
2. **Review error responses** - Comprehensive error handling documented
3. **Test with examples** - All endpoints include working examples
4. **Contact development team** - For additional support

## 🔄 Updates & Maintenance

- **Auto-generated Documentation** - Created using `generate_docs.py`
- **Version Control** - All changes tracked in Git
- **Regular Updates** - Documentation updated with API changes
- **Testing** - All examples tested against live API

---

**🎯 Ready to integrate? Start with [Authentication](auth/README.md) and explore the modules that match your needs!**
