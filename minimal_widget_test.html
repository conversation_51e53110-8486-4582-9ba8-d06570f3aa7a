<!DOCTYPE html>
<html>
<head>
    <title>Minimal Widget Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f0f0f0; }
        .test-button { 
            background: #667eea; 
            color: white; 
            padding: 15px 30px; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
            font-size: 16px;
            margin: 10px;
        }
        .test-button:hover { background: #5a6fd8; }
    </style>
</head>
<body>
    <h1>🤖 Minimal AI Widget Test</h1>
    <p>This is a minimal test to check if the widget files exist and can be loaded.</p>
    
    <button class="test-button" onclick="checkFiles()">Check Widget Files</button>
    <button class="test-button" onclick="loadWidget()">Load Widget</button>
    
    <div id="status"></div>
    
    <script>
        function checkFiles() {
            const status = document.getElementById('status');
            status.innerHTML = '<h3>Checking files...</h3>';
            
            // Try to load CSS
            const css = document.createElement('link');
            css.rel = 'stylesheet';
            css.href = '/static/ai_assistant/css/floating-widget.css';
            css.onload = () => status.innerHTML += '<p>✅ CSS loaded successfully</p>';
            css.onerror = () => status.innerHTML += '<p>❌ CSS failed to load</p>';
            document.head.appendChild(css);
            
            // Try to load JS
            const js = document.createElement('script');
            js.src = '/static/ai_assistant/js/floating-widget.js';
            js.onload = () => {
                status.innerHTML += '<p>✅ JavaScript loaded successfully</p>';
                status.innerHTML += '<p>FloatingAIWidget available: ' + (typeof FloatingAIWidget !== 'undefined') + '</p>';
            };
            js.onerror = () => status.innerHTML += '<p>❌ JavaScript failed to load</p>';
            document.head.appendChild(js);
        }
        
        function loadWidget() {
            if (typeof FloatingAIWidget !== 'undefined') {
                try {
                    window.testWidget = new FloatingAIWidget({
                        apiUrl: '/ai-assistant/api/message/',
                        uploadUrl: '/ai-assistant/api/upload/',
                        enableVoice: true,
                        enableFileUpload: true
                    });
                    document.getElementById('status').innerHTML += '<p>✅ Widget initialized successfully!</p>';
                } catch (error) {
                    document.getElementById('status').innerHTML += '<p>❌ Widget initialization failed: ' + error.message + '</p>';
                }
            } else {
                document.getElementById('status').innerHTML += '<p>❌ FloatingAIWidget class not available</p>';
            }
        }
    </script>
</body>
</html>