{% load static %}
{% load ai_assistant_tags %}
<!-- Floating AI Assistant Widget -->
<!-- Include this template in your base template or specific pages -->

<!-- CSS -->
<link rel="stylesheet" href="{% static 'ai_assistant/css/floating-widget.css' %}">

<!-- Widget will be dynamically created by JavaScript -->

<!-- JavaScript -->
<script src="{% static 'ai_assistant/js/floating-widget.js' %}"></script>

<!-- Widget Configuration -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Get API URLs
    const apiUrls = {% ai_widget_urls %};

    // Get widget configuration
    const widgetConfig = {% ai_widget_config %};

    // Get quick actions
    const quickActions = {% ai_quick_actions %};

    // Initialize widget with configuration
    if (typeof FloatingAIWidget !== 'undefined') {
        window.aiWidget = new FloatingAIWidget({
            apiUrl: apiUrls.messageUrl,
            uploadUrl: apiUrls.uploadUrl,
            historyUrl: apiUrls.historyUrl,
            ...widgetConfig,
            quickActions: quickActions
        });

        // Add version info
        console.log('AI Assistant Widget v{% ai_widget_version %} loaded');
    }
});
</script>

<!-- CSRF Token for AJAX requests -->
{% csrf_token %}

<style>
/* Optional: Custom theme overrides */
:root {
    --ai-primary-color: #667eea;
    --ai-secondary-color: #764ba2;
    --ai-success-color: #2ed573;
    --ai-error-color: #ff4757;
    --ai-text-color: #333;
    --ai-bg-color: #fff;
    --ai-border-color: #e9ecef;
}

/* Dark theme */
[data-theme="dark"] {
    --ai-text-color: #fff;
    --ai-bg-color: #2c2c2c;
    --ai-border-color: #555;
}

/* Custom positioning */
.ai-widget-container.bottom-left {
    bottom: 20px;
    left: 20px;
    right: auto;
}

.ai-widget-container.bottom-left .ai-chat-widget {
    left: 0;
    right: auto;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .ai-widget-container {
        bottom: 10px;
        right: 10px;
    }
    
    .ai-chat-widget {
        width: calc(100vw - 20px);
        height: calc(100vh - 80px);
        bottom: 70px;
        right: 10px;
        left: 10px;
    }
}

/* Accessibility improvements */
.ai-widget-button:focus-visible,
.ai-action-btn:focus-visible {
    outline: 2px solid var(--ai-primary-color);
    outline-offset: 2px;
}

/* Animation preferences */
@media (prefers-reduced-motion: reduce) {
    .ai-chat-widget,
    .ai-message,
    .ai-widget-button {
        animation: none;
        transition: none;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .ai-widget-button {
        border: 2px solid #000;
    }
    
    .ai-message-bubble {
        border: 1px solid #000;
    }
}
</style>
