"""
Django management command to configure AI Assistant settings
"""

from django.core.management.base import BaseCommand
from ai_assistant.models import AIAssistantSettings


class Command(BaseCommand):
    help = 'Configure AI Assistant settings'

    def add_arguments(self, parser):
        parser.add_argument(
            '--disable-rasa',
            action='store_true',
            help='Disable Rasa and use mock responses',
        )
        parser.add_argument(
            '--enable-rasa',
            action='store_true',
            help='Enable Rasa server integration',
        )
        parser.add_argument(
            '--rasa-url',
            type=str,
            help='Set Rasa server URL',
        )
        parser.add_argument(
            '--confidence-threshold',
            type=float,
            help='Set confidence threshold (0.0-1.0)',
        )

    def handle(self, *args, **options):
        # Get or create settings
        settings, created = AIAssistantSettings.objects.get_or_create(
            is_active=True,
            defaults={
                'ollama_model': 'mistral',
                'enable_rasa': True,
                'confidence_threshold': 0.8,
            }
        )

        if created:
            self.stdout.write(
                self.style.SUCCESS('Created new AI Assistant settings')
            )

        # Update settings based on options
        updated = False

        if options['disable_rasa']:
            settings.enable_rasa = False
            updated = True
            self.stdout.write(
                self.style.WARNING('Disabled Rasa - using mock responses')
            )

        if options['enable_rasa']:
            settings.enable_rasa = True
            updated = True
            self.stdout.write(
                self.style.SUCCESS('Enabled Rasa server integration')
            )

        if options['rasa_url']:
            settings.rasa_server_url = options['rasa_url']
            updated = True
            self.stdout.write(
                self.style.SUCCESS(f'Set Rasa URL to: {options["rasa_url"]}')
            )

        if options['confidence_threshold'] is not None:
            if 0.0 <= options['confidence_threshold'] <= 1.0:
                settings.confidence_threshold = options['confidence_threshold']
                updated = True
                self.stdout.write(
                    self.style.SUCCESS(f'Set confidence threshold to: {options["confidence_threshold"]}')
                )
            else:
                self.stdout.write(
                    self.style.ERROR('Confidence threshold must be between 0.0 and 1.0')
                )

        if updated:
            settings.save()
            self.stdout.write(
                self.style.SUCCESS('AI Assistant settings updated successfully')
            )

        # Display current settings
        self.stdout.write('\nCurrent AI Assistant Settings:')
        self.stdout.write(f'  Rasa Enabled: {settings.enable_rasa}')
        self.stdout.write(f'  Rasa URL: {settings.rasa_server_url}')
        self.stdout.write(f'  Confidence Threshold: {settings.confidence_threshold}')
        self.stdout.write(f'  Ollama Model: {settings.ollama_model}')
        self.stdout.write(f'  LangChain Enabled: {settings.enable_langchain}')
        self.stdout.write(f'  Memory Enabled: {settings.enable_memory}')
