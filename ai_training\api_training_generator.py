#!/usr/bin/env python3
"""
Eaglora HRMS AI Training Data Generator

This script generates comprehensive training data for the AI model using the complete
API documentation. It creates 100,000+ training examples for each API endpoint,
mapping natural language queries to API calls.
"""

import json
import random
import os
from datetime import datetime, timedelta
from typing import List, Dict, Any
import itertools

class EagloraAITrainingGenerator:
    def __init__(self):
        self.training_data = []
        self.api_base_url = "http://localhost:8000"
        
        # Load API structure from documentation
        self.api_structure = self.load_api_structure()
        
        # Natural language patterns for different types of queries
        self.query_patterns = {
            "search": [
                "find {entity}", "search for {entity}", "look for {entity}", "show me {entity}",
                "get {entity}", "list {entity}", "display {entity}", "fetch {entity}",
                "I need {entity}", "can you find {entity}", "where is {entity}",
                "show all {entity}", "give me {entity}", "retrieve {entity}"
            ],
            "create": [
                "create {entity}", "add {entity}", "new {entity}", "make {entity}",
                "register {entity}", "insert {entity}", "save {entity}", "submit {entity}",
                "I want to create {entity}", "can you add {entity}", "please create {entity}",
                "need to add {entity}", "let me create {entity}", "help me add {entity}"
            ],
            "update": [
                "update {entity}", "edit {entity}", "modify {entity}", "change {entity}",
                "alter {entity}", "revise {entity}", "correct {entity}", "fix {entity}",
                "I want to update {entity}", "can you edit {entity}", "please modify {entity}",
                "need to change {entity}", "let me update {entity}", "help me edit {entity}"
            ],
            "delete": [
                "delete {entity}", "remove {entity}", "cancel {entity}", "drop {entity}",
                "eliminate {entity}", "erase {entity}", "clear {entity}", "destroy {entity}",
                "I want to delete {entity}", "can you remove {entity}", "please delete {entity}",
                "need to remove {entity}", "let me delete {entity}", "help me remove {entity}"
            ],
            "approve": [
                "approve {entity}", "accept {entity}", "confirm {entity}", "authorize {entity}",
                "validate {entity}", "endorse {entity}", "ratify {entity}", "sanction {entity}",
                "I want to approve {entity}", "can you approve {entity}", "please approve {entity}",
                "need to approve {entity}", "let me approve {entity}", "help me approve {entity}"
            ],
            "reject": [
                "reject {entity}", "decline {entity}", "deny {entity}", "refuse {entity}",
                "dismiss {entity}", "turn down {entity}", "disapprove {entity}",
                "I want to reject {entity}", "can you reject {entity}", "please reject {entity}",
                "need to reject {entity}", "let me reject {entity}", "help me reject {entity}"
            ]
        }
        
        # Entity variations for different modules
        self.entity_variations = {
            "employee": [
                "employee", "staff", "worker", "team member", "colleague", "person",
                "user", "individual", "member", "personnel", "workforce", "human resource"
            ],
            "attendance": [
                "attendance", "presence", "check-in", "clock-in", "time tracking", "work hours",
                "punctuality", "time record", "work time", "office hours", "shift time"
            ],
            "leave": [
                "leave", "vacation", "holiday", "time off", "absence", "break",
                "sick leave", "annual leave", "personal leave", "day off", "leave request"
            ],
            "payroll": [
                "payroll", "salary", "pay", "wages", "compensation", "payment",
                "payslip", "earnings", "income", "remuneration", "stipend"
            ],
            "asset": [
                "asset", "equipment", "device", "item", "resource", "property",
                "tool", "machine", "hardware", "inventory", "material"
            ]
        }
        
        # Common search terms and filters
        self.search_terms = [
            "john", "jane", "smith", "doe", "admin", "manager", "developer",
            "active", "inactive", "pending", "approved", "rejected", "IT", "HR",
            "today", "yesterday", "this week", "this month", "last month"
        ]
        
        # Humanized query variations
        self.humanized_patterns = [
            "Can you help me {action}?",
            "I need to {action}",
            "Please {action}",
            "How do I {action}?",
            "I want to {action}",
            "Could you {action}?",
            "I'm looking to {action}",
            "Help me {action}",
            "I'd like to {action}",
            "Show me how to {action}"
        ]

    def load_api_structure(self):
        """Load API structure from the documentation"""
        return {
            "auth": {
                "base_url": "/api/auth/",
                "endpoints": [
                    {"name": "login", "url": "/login/", "method": "POST", "entity": "authentication"}
                ]
            },
            "employee": {
                "base_url": "/api/employee/",
                "endpoints": [
                    {"name": "employees-list", "url": "/employees/", "method": "GET", "entity": "employee", "supports_search": True},
                    {"name": "employee-create", "url": "/employees/", "method": "POST", "entity": "employee"},
                    {"name": "employee-detail", "url": "/employees/{id}/", "method": "GET", "entity": "employee"},
                    {"name": "employee-update", "url": "/employees/{id}/", "method": "PUT", "entity": "employee"},
                    {"name": "employee-delete", "url": "/employees/{id}/", "method": "DELETE", "entity": "employee"},
                    {"name": "employee-types-list", "url": "/employee-type/", "method": "GET", "entity": "employee type", "supports_search": True},
                    {"name": "employee-work-info-list", "url": "/employee-work-information/", "method": "GET", "entity": "work information", "supports_search": True},
                    {"name": "employee-search-advanced", "url": "/list/employees/", "method": "GET", "entity": "employee", "supports_search": True},
                    {"name": "employee-selector", "url": "/employee-selector/", "method": "GET", "entity": "employee", "supports_search": True},
                    {"name": "employee-bulk-update", "url": "/employee-bulk-update/", "method": "PUT", "entity": "employee"},
                    {"name": "disciplinary-action-list", "url": "/disciplinary-action/", "method": "GET", "entity": "disciplinary action", "supports_search": True},
                    {"name": "policies-list", "url": "/policies/", "method": "GET", "entity": "policy", "supports_search": True},
                    {"name": "documents-list", "url": "/documents/", "method": "GET", "entity": "document", "supports_search": True}
                ]
            },
            "attendance": {
                "base_url": "/api/attendance/",
                "endpoints": [
                    {"name": "clock-in", "url": "/clock-in/", "method": "POST", "entity": "clock in"},
                    {"name": "clock-out", "url": "/clock-out/", "method": "POST", "entity": "clock out"},
                    {"name": "attendance-list", "url": "/attendance/", "method": "GET", "entity": "attendance", "supports_search": True},
                    {"name": "attendance-create", "url": "/attendance/", "method": "POST", "entity": "attendance"},
                    {"name": "attendance-request-list", "url": "/attendance-request/", "method": "GET", "entity": "attendance request", "supports_search": True},
                    {"name": "attendance-request-create", "url": "/attendance-request/", "method": "POST", "entity": "attendance request"},
                    {"name": "attendance-request-approve", "url": "/attendance-request-approve/{id}/", "method": "POST", "entity": "attendance request"},
                    {"name": "today-attendance", "url": "/today-attendance/", "method": "GET", "entity": "today's attendance"},
                    {"name": "offline-employees-list", "url": "/offline-employees/list/", "method": "GET", "entity": "offline employee", "supports_search": True}
                ]
            },
            "leave": {
                "base_url": "/api/leave/",
                "endpoints": [
                    {"name": "leave-request-list", "url": "/leave-request/", "method": "GET", "entity": "leave request", "supports_search": True},
                    {"name": "leave-request-create", "url": "/leave-request/", "method": "POST", "entity": "leave request"},
                    {"name": "leave-request-approve", "url": "/leave-request-approve/{id}/", "method": "POST", "entity": "leave request"},
                    {"name": "leave-request-reject", "url": "/leave-request-reject/{id}/", "method": "POST", "entity": "leave request"},
                    {"name": "leave-type-list", "url": "/leave-type/", "method": "GET", "entity": "leave type", "supports_search": True},
                    {"name": "leave-allocation-list", "url": "/leave-allocation/", "method": "GET", "entity": "leave allocation", "supports_search": True},
                    {"name": "holiday-list", "url": "/holiday/", "method": "GET", "entity": "holiday", "supports_search": True}
                ]
            },
            "payroll": {
                "base_url": "/api/payroll/",
                "endpoints": [
                    {"name": "contract-list", "url": "/contract/", "method": "GET", "entity": "contract", "supports_search": True},
                    {"name": "payslip-list", "url": "/payslip/", "method": "GET", "entity": "payslip", "supports_search": True},
                    {"name": "allowance-list", "url": "/allowance/", "method": "GET", "entity": "allowance", "supports_search": True},
                    {"name": "deduction-list", "url": "/deduction/", "method": "GET", "entity": "deduction", "supports_search": True},
                    {"name": "loan-list", "url": "/loan/", "method": "GET", "entity": "loan", "supports_search": True},
                    {"name": "bonus-list", "url": "/bonus/", "method": "GET", "entity": "bonus", "supports_search": True}
                ]
            },
            "asset": {
                "base_url": "/api/asset/",
                "endpoints": [
                    {"name": "asset-list", "url": "/asset/", "method": "GET", "entity": "asset", "supports_search": True},
                    {"name": "asset-create", "url": "/asset/", "method": "POST", "entity": "asset"},
                    {"name": "asset-category-list", "url": "/asset-category/", "method": "GET", "entity": "asset category", "supports_search": True},
                    {"name": "asset-request-list", "url": "/asset-request/", "method": "GET", "entity": "asset request", "supports_search": True},
                    {"name": "asset-request-approve", "url": "/asset-request-approve/{id}/", "method": "POST", "entity": "asset request"}
                ]
            },
            "base": {
                "base_url": "/api/base/",
                "endpoints": [
                    {"name": "company-list", "url": "/company/", "method": "GET", "entity": "company", "supports_search": True},
                    {"name": "department-list", "url": "/department/", "method": "GET", "entity": "department", "supports_search": True},
                    {"name": "job-position-list", "url": "/job-position/", "method": "GET", "entity": "job position", "supports_search": True},
                    {"name": "shift-list", "url": "/shift/", "method": "GET", "entity": "shift", "supports_search": True}
                ]
            },
            "notifications": {
                "base_url": "/api/notifications/",
                "endpoints": [
                    {"name": "notification-list", "url": "/notification/", "method": "GET", "entity": "notification", "supports_search": True},
                    {"name": "notification-mark-read", "url": "/notification-mark-read/{id}/", "method": "POST", "entity": "notification"}
                ]
            },
            "ai-assistant": {
                "base_url": "/ai-assistant/api/",
                "endpoints": [
                    {"name": "chat-message", "url": "/message/", "method": "POST", "entity": "message"},
                    {"name": "employee-search-ai", "url": "/employee/search/", "method": "GET", "entity": "employee", "supports_search": True}
                ]
            }
        }

    def generate_search_variations(self, base_query: str, search_term: str) -> List[str]:
        """Generate variations of search queries"""
        variations = []
        
        # Direct search variations
        search_patterns = [
            f"{base_query} {search_term}",
            f"{base_query} for {search_term}",
            f"{base_query} with {search_term}",
            f"{base_query} containing {search_term}",
            f"{base_query} named {search_term}",
            f"{base_query} called {search_term}",
            f"search {base_query} {search_term}",
            f"find {base_query} {search_term}",
            f"look for {base_query} {search_term}",
            f"show me {base_query} {search_term}"
        ]
        
        # Humanized variations
        humanized = [
            f"Can you help me find {base_query} {search_term}?",
            f"I'm looking for {base_query} {search_term}",
            f"Please show me {base_query} {search_term}",
            f"I need to find {base_query} {search_term}",
            f"Could you search for {base_query} {search_term}?",
            f"Help me locate {base_query} {search_term}",
            f"I want to see {base_query} {search_term}",
            f"Display {base_query} {search_term} please",
            f"Get me {base_query} {search_term}",
            f"Retrieve {base_query} {search_term} for me"
        ]
        
        variations.extend(search_patterns)
        variations.extend(humanized)
        
        return variations

    def generate_training_examples_for_endpoint(self, module: str, endpoint: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate comprehensive training examples for a specific endpoint"""
        examples = []
        entity = endpoint.get("entity", "item")
        method = endpoint["method"]
        url = endpoint["url"]
        base_url = self.api_structure[module]["base_url"]
        full_url = f"{self.api_base_url}{base_url.rstrip('/')}{url}"
        
        # Get entity variations
        entity_vars = self.entity_variations.get(module, [entity])
        
        # Generate examples based on HTTP method
        if method == "GET":
            # List/Search operations
            action_patterns = self.query_patterns["search"]
            
            for entity_var in entity_vars:
                for pattern in action_patterns:
                    query = pattern.format(entity=entity_var)
                    
                    # Basic query without search
                    examples.append({
                        "input": query,
                        "output": {
                            "intent": "api_call",
                            "api_endpoint": full_url,
                            "method": method,
                            "module": module,
                            "action": "list",
                            "entity": entity,
                            "confidence": 0.95
                        }
                    })
                    
                    # Humanized variations
                    for human_pattern in self.humanized_patterns:
                        human_query = human_pattern.format(action=f"find {entity_var}")
                        examples.append({
                            "input": human_query,
                            "output": {
                                "intent": "api_call",
                                "api_endpoint": full_url,
                                "method": method,
                                "module": module,
                                "action": "list",
                                "entity": entity,
                                "confidence": 0.92
                            }
                        })
                    
                    # Search variations if endpoint supports search
                    if endpoint.get("supports_search", False):
                        for search_term in self.search_terms:
                            search_variations = self.generate_search_variations(entity_var, search_term)
                            
                            for search_query in search_variations:
                                search_url = f"{full_url}?search={search_term}"
                                examples.append({
                                    "input": search_query,
                                    "output": {
                                        "intent": "api_call",
                                        "api_endpoint": search_url,
                                        "method": method,
                                        "module": module,
                                        "action": "search",
                                        "entity": entity,
                                        "search_term": search_term,
                                        "confidence": 0.98
                                    }
                                })
        
        elif method == "POST":
            # Create operations
            if "approve" in endpoint["name"]:
                action_patterns = self.query_patterns["approve"]
                action_type = "approve"
            elif "reject" in endpoint["name"]:
                action_patterns = self.query_patterns["reject"]
                action_type = "reject"
            elif "clock-in" in endpoint["name"]:
                action_patterns = ["clock in", "check in", "start work", "arrive", "punch in"]
                action_type = "clock_in"
            elif "clock-out" in endpoint["name"]:
                action_patterns = ["clock out", "check out", "end work", "leave", "punch out"]
                action_type = "clock_out"
            else:
                action_patterns = self.query_patterns["create"]
                action_type = "create"
            
            for entity_var in entity_vars:
                for pattern in action_patterns:
                    if isinstance(pattern, str) and "{entity}" in pattern:
                        query = pattern.format(entity=entity_var)
                    else:
                        query = f"{pattern} {entity_var}"
                    
                    examples.append({
                        "input": query,
                        "output": {
                            "intent": "api_call",
                            "api_endpoint": full_url,
                            "method": method,
                            "module": module,
                            "action": action_type,
                            "entity": entity,
                            "confidence": 0.95
                        }
                    })
                    
                    # Humanized variations
                    for human_pattern in self.humanized_patterns:
                        human_query = human_pattern.format(action=query)
                        examples.append({
                            "input": human_query,
                            "output": {
                                "intent": "api_call",
                                "api_endpoint": full_url,
                                "method": method,
                                "module": module,
                                "action": action_type,
                                "entity": entity,
                                "confidence": 0.92
                            }
                        })
        
        elif method == "PUT":
            # Update operations
            action_patterns = self.query_patterns["update"]
            
            for entity_var in entity_vars:
                for pattern in action_patterns:
                    query = pattern.format(entity=entity_var)
                    
                    examples.append({
                        "input": query,
                        "output": {
                            "intent": "api_call",
                            "api_endpoint": full_url,
                            "method": method,
                            "module": module,
                            "action": "update",
                            "entity": entity,
                            "confidence": 0.95
                        }
                    })
        
        elif method == "DELETE":
            # Delete operations
            action_patterns = self.query_patterns["delete"]
            
            for entity_var in entity_vars:
                for pattern in action_patterns:
                    query = pattern.format(entity=entity_var)
                    
                    examples.append({
                        "input": query,
                        "output": {
                            "intent": "api_call",
                            "api_endpoint": full_url,
                            "method": method,
                            "module": module,
                            "action": "delete",
                            "entity": entity,
                            "confidence": 0.95
                        }
                    })
        
        return examples

    def generate_contextual_variations(self, base_examples: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Generate contextual variations of training examples"""
        variations = []
        
        # Context patterns
        contexts = [
            "In the HRMS system, ",
            "Using Eaglora, ",
            "From the dashboard, ",
            "In the employee portal, ",
            "Through the system, ",
            "Via the application, "
        ]
        
        # Time contexts
        time_contexts = [
            "today", "this week", "this month", "yesterday", "last week",
            "now", "currently", "right now", "at the moment"
        ]
        
        # Urgency contexts
        urgency_contexts = [
            "urgently", "quickly", "immediately", "as soon as possible",
            "right away", "ASAP", "fast", "promptly"
        ]
        
        for example in base_examples:
            original_input = example["input"]
            
            # Add context variations
            for context in contexts:
                variations.append({
                    "input": f"{context}{original_input}",
                    "output": example["output"]
                })
            
            # Add time context variations
            for time_ctx in time_contexts:
                variations.append({
                    "input": f"{original_input} {time_ctx}",
                    "output": example["output"]
                })
                
                variations.append({
                    "input": f"{time_ctx} {original_input}",
                    "output": example["output"]
                })
            
            # Add urgency variations
            for urgency in urgency_contexts:
                variations.append({
                    "input": f"{original_input} {urgency}",
                    "output": example["output"]
                })
                
                variations.append({
                    "input": f"I need to {original_input} {urgency}",
                    "output": example["output"]
                })
        
        return variations

    def generate_all_training_data(self) -> Dict[str, List[Dict[str, Any]]]:
        """Generate comprehensive training data for all API endpoints"""
        all_training_data = {}
        
        print("🚀 Starting comprehensive AI training data generation...")
        print(f"📊 Target: 10,000+ examples per API endpoint")
        
        total_endpoints = 0
        total_examples = 0
        
        for module_name, module_data in self.api_structure.items():
            print(f"\n📁 Processing {module_name} module...")
            module_training_data = []
            
            for endpoint in module_data["endpoints"]:
                endpoint_name = endpoint["name"]
                print(f"  🔗 Generating training data for {endpoint_name}...")
                
                # Generate base examples
                base_examples = self.generate_training_examples_for_endpoint(module_name, endpoint)
                
                # Generate contextual variations
                contextual_examples = self.generate_contextual_variations(base_examples)
                
                # Combine all examples
                all_examples = base_examples + contextual_examples
                
                # Duplicate and vary to reach target examples (reduced for efficiency)
                target_count = 10000  # Reduced from 100,000 to 10,000 for efficiency
                current_count = len(all_examples)

                # Generate variations more efficiently
                variation_cycles = 0
                max_cycles = 10  # Prevent infinite loops

                while current_count < target_count and variation_cycles < max_cycles:
                    batch_size = min(500, target_count - current_count)

                    # Add variations with different phrasings
                    for i, example in enumerate(base_examples[:batch_size]):
                        if current_count >= target_count:
                            break

                        # Add natural variations
                        varied_input = self.add_natural_variations(example["input"])
                        all_examples.append({
                            "input": varied_input,
                            "output": example["output"]
                        })
                        current_count += 1

                    variation_cycles += 1
                
                # Store endpoint training data
                final_examples = all_examples[:target_count]
                endpoint_data = {
                    "endpoint_name": endpoint_name,
                    "module": module_name,
                    "examples": final_examples,
                    "count": len(final_examples)
                }
                
                module_training_data.append(endpoint_data)
                total_examples += endpoint_data["count"]
                
                print(f"    ✅ Generated {endpoint_data['count']:,} examples for {endpoint_name}")
            
            all_training_data[module_name] = module_training_data
            total_endpoints += len(module_data["endpoints"])
        
        print(f"\n🎉 Training data generation complete!")
        print(f"📊 Total endpoints: {total_endpoints}")
        print(f"📊 Total training examples: {total_examples:,}")
        print(f"📊 Average examples per endpoint: {total_examples // total_endpoints:,}")
        
        return all_training_data

    def add_natural_variations(self, text: str) -> str:
        """Add natural variations to text"""
        variations = [
            text.lower(),
            text.upper(),
            text.capitalize(),
            text.replace("employee", "staff member"),
            text.replace("show", "display"),
            text.replace("get", "fetch"),
            text.replace("find", "locate"),
            text.replace("list", "show all"),
            f"please {text}",
            f"can you {text}",
            f"I want to {text}",
            f"help me {text}",
            f"{text} please",
            f"{text} now",
            f"{text} for me"
        ]
        
        return random.choice(variations)

    def save_training_data(self, training_data: Dict[str, List[Dict[str, Any]]], output_dir: str = "ai_training/data"):
        """Save training data to files"""
        os.makedirs(output_dir, exist_ok=True)
        
        print(f"\n💾 Saving training data to {output_dir}...")
        
        # Save complete dataset
        complete_file = os.path.join(output_dir, "complete_training_data.json")
        with open(complete_file, 'w') as f:
            json.dump(training_data, f, indent=2)
        
        # Save individual module files
        for module_name, module_data in training_data.items():
            module_file = os.path.join(output_dir, f"{module_name}_training_data.json")
            with open(module_file, 'w') as f:
                json.dump(module_data, f, indent=2)
            
            print(f"  ✅ Saved {module_name} training data")
        
        # Save summary statistics
        stats = self.generate_statistics(training_data)
        stats_file = os.path.join(output_dir, "training_statistics.json")
        with open(stats_file, 'w') as f:
            json.dump(stats, f, indent=2)
        
        print(f"  ✅ Saved training statistics")
        print(f"💾 All training data saved successfully!")

    def generate_statistics(self, training_data: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """Generate statistics about the training data"""
        stats = {
            "generation_date": datetime.now().isoformat(),
            "total_modules": len(training_data),
            "modules": {},
            "overall_stats": {
                "total_endpoints": 0,
                "total_examples": 0,
                "average_examples_per_endpoint": 0
            }
        }
        
        total_endpoints = 0
        total_examples = 0
        
        for module_name, module_data in training_data.items():
            module_endpoints = len(module_data)
            module_examples = sum(endpoint["count"] for endpoint in module_data)
            
            stats["modules"][module_name] = {
                "endpoints": module_endpoints,
                "total_examples": module_examples,
                "average_examples_per_endpoint": module_examples // module_endpoints if module_endpoints > 0 else 0,
                "endpoint_details": [
                    {
                        "name": endpoint["endpoint_name"],
                        "examples": endpoint["count"]
                    }
                    for endpoint in module_data
                ]
            }
            
            total_endpoints += module_endpoints
            total_examples += module_examples
        
        stats["overall_stats"]["total_endpoints"] = total_endpoints
        stats["overall_stats"]["total_examples"] = total_examples
        stats["overall_stats"]["average_examples_per_endpoint"] = total_examples // total_endpoints if total_endpoints > 0 else 0
        
        return stats

def main():
    """Main function to generate training data"""
    print("🤖 Eaglora HRMS AI Training Data Generator")
    print("=" * 50)
    
    generator = EagloraAITrainingGenerator()
    
    # Generate all training data
    training_data = generator.generate_all_training_data()
    
    # Save training data
    generator.save_training_data(training_data)
    
    print("\n🎯 Training data generation completed successfully!")
    print("📁 Files created:")
    print("  - ai_training/data/complete_training_data.json")
    print("  - ai_training/data/{module}_training_data.json (for each module)")
    print("  - ai_training/data/training_statistics.json")
    
    print("\n🚀 Ready to train the AI model with comprehensive HRMS API knowledge!")

if __name__ == "__main__":
    main()
