#!/usr/bin/env python3
"""
Eaglora HRMS AI Integration Module

This module integrates the trained AI model with the existing AI assistant,
providing natural language to API mapping capabilities.
"""

import json
import os
import requests
from typing import Dict, Any, Optional, List
from datetime import datetime
import logging

# ML Libraries
try:
    import joblib
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.preprocessing import LabelEncoder
except ImportError:
    print("Installing required ML libraries...")
    os.system("pip install scikit-learn joblib")
    import joblib
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.preprocessing import LabelEncoder

class EagloraAIIntegration:
    """
    Main AI integration class that loads trained models and provides
    natural language to API mapping functionality.
    """
    
    def __init__(self, models_dir: str = "ai_training/models"):
        self.models_dir = models_dir
        self.models = {}
        self.vectorizers = {}
        self.label_encoders = {}
        self.api_mappings = {}
        self.is_loaded = False
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # Load models if available
        self.load_models()
    
    def load_models(self) -> bool:
        """Load all trained models from disk"""
        try:
            self.logger.info("🔄 Loading trained AI models...")
            
            # Check if models directory exists
            if not os.path.exists(self.models_dir):
                self.logger.warning(f"Models directory not found: {self.models_dir}")
                return False
            
            # Load model package info
            package_file = os.path.join(self.models_dir, "model_package.json")
            if not os.path.exists(package_file):
                self.logger.warning("Model package file not found")
                return False
            
            with open(package_file, 'r') as f:
                package_info = json.load(f)
            
            # Load models
            for model_name, model_file in package_info["models"].items():
                model_path = os.path.join(self.models_dir, model_file)
                if os.path.exists(model_path):
                    self.models[model_name] = joblib.load(model_path)
                    self.logger.info(f"  ✅ Loaded {model_name}")
            
            # Load vectorizers
            for vec_name, vec_file in package_info["vectorizers"].items():
                vec_path = os.path.join(self.models_dir, vec_file)
                if os.path.exists(vec_path):
                    self.vectorizers[vec_name] = joblib.load(vec_path)
                    self.logger.info(f"  ✅ Loaded {vec_name} vectorizer")
            
            # Load label encoders
            for enc_name, enc_file in package_info["label_encoders"].items():
                enc_path = os.path.join(self.models_dir, enc_file)
                if os.path.exists(enc_path):
                    self.label_encoders[enc_name] = joblib.load(enc_path)
                    self.logger.info(f"  ✅ Loaded {enc_name} encoder")
            
            # Load API mappings
            mappings_file = os.path.join(self.models_dir, "api_mappings.json")
            if os.path.exists(mappings_file):
                with open(mappings_file, 'r') as f:
                    self.api_mappings = json.load(f)
                self.logger.info("  ✅ Loaded API mappings")
            
            self.is_loaded = True
            self.logger.info("🎉 All AI models loaded successfully!")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error loading models: {str(e)}")
            return False
    
    def predict_api_call(self, user_input: str) -> Dict[str, Any]:
        """
        Predict the appropriate API call for a given user input
        
        Args:
            user_input: Natural language query from user
            
        Returns:
            Dictionary containing API call details
        """
        if not self.is_loaded:
            return {
                "error": "AI models not loaded",
                "success": False,
                "message": "Please ensure AI models are trained and available"
            }
        
        try:
            # Preprocess input
            cleaned_input = self.preprocess_input(user_input)
            
            # Predict intent using trained model
            intent_result = self.predict_intent(cleaned_input)
            
            # Extract search terms if applicable
            search_term = self.extract_search_term(cleaned_input, intent_result)
            
            # Build final API call
            api_call = self.build_api_call(intent_result, search_term)
            
            # Add metadata
            api_call.update({
                "raw_input": user_input,
                "processed_input": cleaned_input,
                "timestamp": datetime.now().isoformat(),
                "success": True
            })
            
            return api_call
            
        except Exception as e:
            self.logger.error(f"Error predicting API call: {str(e)}")
            return {
                "error": str(e),
                "success": False,
                "raw_input": user_input
            }
    
    def predict_intent(self, text: str) -> Dict[str, Any]:
        """Predict the intent from preprocessed text"""
        if "intent_classifier" not in self.models:
            raise ValueError("Intent classifier model not available")
        
        # Vectorize input
        intent_vector = self.vectorizers["intent"].transform([text])
        
        # Predict intent
        intent_pred = self.models["intent_classifier"].predict(intent_vector)[0]
        intent_proba = self.models["intent_classifier"].predict_proba(intent_vector)[0]
        confidence = max(intent_proba)
        
        # Decode intent
        intent_label = self.label_encoders["intent"].inverse_transform([intent_pred])[0]
        
        return {
            "intent": intent_label,
            "confidence": float(confidence),
            "prediction": int(intent_pred)
        }
    
    def extract_search_term(self, text: str, intent_result: Dict[str, Any]) -> Optional[str]:
        """Extract search term from text if applicable"""
        intent_label = intent_result["intent"]
        api_info = self.api_mappings.get(intent_label, {})
        
        # Only extract search terms for endpoints that support search
        if not api_info.get("supports_search", False):
            return None
        
        # Try ML-based extraction if model is available
        if "search_extractor" in self.models:
            try:
                search_vector = self.vectorizers["search"].transform([text])
                search_pred = self.models["search_extractor"].predict(search_vector)[0]
                search_term = self.label_encoders["search"].inverse_transform([search_pred])[0]
                return search_term
            except Exception as e:
                self.logger.warning(f"ML search extraction failed: {e}")
        
        # Fallback to rule-based extraction
        return self.extract_search_term_fallback(text)
    
    def extract_search_term_fallback(self, text: str) -> str:
        """Fallback method for search term extraction using rules"""
        import re
        
        # Common patterns for search terms
        patterns = [
            r'find\s+(?:employee\s+)?(\w+)',
            r'search\s+for\s+(?:employee\s+)?(\w+)',
            r'look\s+for\s+(?:employee\s+)?(\w+)',
            r'show\s+me\s+(?:employee\s+)?(\w+)',
            r'get\s+(?:employee\s+)?(\w+)',
            r'named\s+(\w+)',
            r'called\s+(\w+)',
            r'with\s+name\s+(\w+)',
            r'containing\s+(\w+)',
            r'employee\s+(\w+)',
            r'staff\s+(\w+)',
            r'user\s+(\w+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text.lower())
            if match:
                return match.group(1)
        
        # Extract potential names or identifiers
        words = text.lower().split()
        common_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 
            'of', 'with', 'by', 'from', 'up', 'about', 'into', 'through', 'during', 
            'before', 'after', 'above', 'below', 'between', 'among', 'find', 'search', 
            'show', 'get', 'list', 'display', 'employee', 'staff', 'user', 'person', 
            'me', 'please', 'can', 'you', 'help', 'want', 'need', 'looking', 'for',
            'all', 'any', 'some', 'many', 'few', 'several', 'today', 'yesterday',
            'now', 'currently', 'active', 'inactive'
        }
        
        # Look for potential search terms (names, IDs, etc.)
        for word in words:
            if (word not in common_words and 
                len(word) > 2 and 
                not word.isdigit() and 
                word.isalnum()):
                return word
        
        return "all"
    
    def build_api_call(self, intent_result: Dict[str, Any], search_term: Optional[str]) -> Dict[str, Any]:
        """Build the final API call from intent and search term"""
        intent_label = intent_result["intent"]
        api_info = self.api_mappings.get(intent_label, {})
        
        if not api_info:
            raise ValueError(f"No API mapping found for intent: {intent_label}")
        
        # Build base API endpoint
        api_endpoint = api_info["api_endpoint"]
        
        # Add search parameter if applicable
        if search_term and api_info.get("supports_search", False):
            separator = "&" if "?" in api_endpoint else "?"
            api_endpoint += f"{separator}search={search_term}"
        
        return {
            "intent": intent_label,
            "confidence": intent_result["confidence"],
            "api_endpoint": api_endpoint,
            "method": api_info.get("method", "GET"),
            "module": api_info.get("module", ""),
            "action": api_info.get("action", ""),
            "entity": api_info.get("entity", ""),
            "search_term": search_term,
            "supports_search": api_info.get("supports_search", False)
        }
    
    def preprocess_input(self, text: str) -> str:
        """Preprocess user input for better prediction"""
        # Convert to lowercase
        text = text.lower()
        
        # Remove extra whitespace
        text = ' '.join(text.split())
        
        # Remove punctuation at the end
        text = text.rstrip('.,!?;:')
        
        # Handle common contractions
        contractions = {
            "i'm": "i am",
            "you're": "you are",
            "it's": "it is",
            "that's": "that is",
            "what's": "what is",
            "where's": "where is",
            "how's": "how is",
            "i'd": "i would",
            "you'd": "you would",
            "i'll": "i will",
            "you'll": "you will",
            "won't": "will not",
            "can't": "cannot",
            "don't": "do not",
            "doesn't": "does not",
            "didn't": "did not",
            "isn't": "is not",
            "aren't": "are not",
            "wasn't": "was not",
            "weren't": "were not"
        }
        
        for contraction, expansion in contractions.items():
            text = text.replace(contraction, expansion)
        
        return text
    
    def execute_api_call(self, api_call: Dict[str, Any], base_url: str = "http://localhost:8000") -> Dict[str, Any]:
        """
        Execute the predicted API call
        
        Args:
            api_call: API call details from predict_api_call
            base_url: Base URL of the HRMS API
            
        Returns:
            API response or error details
        """
        if not api_call.get("success", False):
            return api_call
        
        try:
            # Build full URL
            endpoint = api_call["api_endpoint"]
            if not endpoint.startswith("http"):
                full_url = f"{base_url.rstrip('/')}{endpoint}"
            else:
                full_url = endpoint
            
            # Prepare request
            method = api_call.get("method", "GET").upper()
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/json"
            }
            
            # Execute request
            self.logger.info(f"Executing {method} {full_url}")
            
            if method == "GET":
                response = requests.get(full_url, headers=headers, timeout=30)
            elif method == "POST":
                response = requests.post(full_url, headers=headers, json={}, timeout=30)
            elif method == "PUT":
                response = requests.put(full_url, headers=headers, json={}, timeout=30)
            elif method == "DELETE":
                response = requests.delete(full_url, headers=headers, timeout=30)
            else:
                return {
                    "error": f"Unsupported HTTP method: {method}",
                    "success": False
                }
            
            # Process response
            result = {
                "success": response.status_code < 400,
                "status_code": response.status_code,
                "url": full_url,
                "method": method,
                "api_call": api_call
            }
            
            try:
                result["data"] = response.json()
            except:
                result["data"] = response.text
            
            if not result["success"]:
                result["error"] = f"API call failed with status {response.status_code}"
            
            return result
            
        except requests.exceptions.RequestException as e:
            return {
                "error": f"Request failed: {str(e)}",
                "success": False,
                "api_call": api_call
            }
        except Exception as e:
            return {
                "error": f"Execution failed: {str(e)}",
                "success": False,
                "api_call": api_call
            }
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about loaded models"""
        return {
            "is_loaded": self.is_loaded,
            "models_available": list(self.models.keys()),
            "vectorizers_available": list(self.vectorizers.keys()),
            "label_encoders_available": list(self.label_encoders.keys()),
            "api_mappings_count": len(self.api_mappings),
            "supported_intents": list(self.api_mappings.keys()) if self.api_mappings else []
        }

# Global instance for easy import
ai_integration = EagloraAIIntegration()

def predict_and_execute(user_input: str, base_url: str = "http://localhost:8000") -> Dict[str, Any]:
    """
    Convenience function to predict and execute API call in one step
    
    Args:
        user_input: Natural language query
        base_url: Base URL of the HRMS API
        
    Returns:
        Combined prediction and execution result
    """
    # Predict API call
    prediction = ai_integration.predict_api_call(user_input)
    
    if not prediction.get("success", False):
        return prediction
    
    # Execute API call
    execution_result = ai_integration.execute_api_call(prediction, base_url)
    
    return {
        "prediction": prediction,
        "execution": execution_result,
        "success": execution_result.get("success", False)
    }

def main():
    """Test the AI integration"""
    print("🤖 Testing Eaglora AI Integration")
    print("=" * 50)
    
    # Test queries
    test_queries = [
        "find employee john",
        "search for staff named jane",
        "show me all employees",
        "clock in now",
        "I want to clock out",
        "list all leave requests",
        "show today's attendance",
        "get my payslip"
    ]
    
    print(f"Model Info: {ai_integration.get_model_info()}")
    print()
    
    for query in test_queries:
        print(f"Query: {query}")
        result = ai_integration.predict_api_call(query)
        
        if result.get("success"):
            print(f"  → {result['method']} {result['api_endpoint']}")
            print(f"  → Module: {result['module']}, Action: {result['action']}")
            print(f"  → Confidence: {result['confidence']:.3f}")
            if result.get('search_term'):
                print(f"  → Search: {result['search_term']}")
        else:
            print(f"  → Error: {result.get('error', 'Unknown error')}")
        print()

if __name__ == "__main__":
    main()
