/* Floating AI Assistant Widget Styles */

/* Widget Container */
.ai-widget-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 9999;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Floating Button */
.ai-widget-button {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.ai-widget-button:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(102, 126, 234, 0.6);
}

.ai-widget-button.active {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.ai-widget-icon {
    width: 24px;
    height: 24px;
    fill: white;
    transition: transform 0.3s ease;
}

.ai-widget-button.active .ai-widget-icon {
    transform: rotate(45deg);
}

/* Notification Badge */
.ai-widget-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ff4757;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Chat Widget */
.ai-chat-widget {
    position: absolute;
    bottom: 80px;
    right: 0;
    width: 380px;
    height: 500px;
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    display: none;
    flex-direction: column;
    overflow: hidden;
    transform: scale(0.8) translateY(20px);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.ai-chat-widget.active {
    display: flex;
    transform: scale(1) translateY(0);
    opacity: 1;
}

/* Widget Header */
.ai-widget-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.ai-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.ai-info h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.ai-status {
    font-size: 12px;
    opacity: 0.9;
    display: flex;
    align-items: center;
    gap: 5px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #2ed573;
    animation: blink 2s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

/* Messages Container */
.ai-messages-container {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #f8f9fa;
    scroll-behavior: smooth;
}

.ai-messages-container::-webkit-scrollbar {
    width: 4px;
}

.ai-messages-container::-webkit-scrollbar-track {
    background: transparent;
}

.ai-messages-container::-webkit-scrollbar-thumb {
    background: #ddd;
    border-radius: 2px;
}

/* Message Styles */
.ai-message {
    margin-bottom: 16px;
    animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.ai-message-bubble {
    max-width: 80%;
    padding: 12px 16px;
    border-radius: 18px;
    word-wrap: break-word;
    position: relative;
}

.ai-message.user {
    text-align: right;
}

.ai-message.user .ai-message-bubble {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin-left: auto;
    border-bottom-right-radius: 6px;
}

.ai-message.bot .ai-message-bubble {
    background: white;
    color: #333;
    border: 1px solid #e9ecef;
    border-bottom-left-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.ai-message-time {
    font-size: 11px;
    opacity: 0.6;
    margin-top: 4px;
}

/* Typing Indicator */
.ai-typing-indicator {
    display: none;
    padding: 12px 16px;
    background: white;
    border-radius: 18px;
    border-bottom-left-radius: 6px;
    max-width: 80px;
    border: 1px solid #e9ecef;
}

.ai-typing-dots {
    display: flex;
    gap: 4px;
}

.ai-typing-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #999;
    animation: typing 1.4s infinite;
}

.ai-typing-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.ai-typing-dot:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.4;
    }
    30% {
        transform: translateY(-10px);
        opacity: 1;
    }
}

/* Input Container */
.ai-input-container {
    padding: 20px;
    background: white;
    border-top: 1px solid #e9ecef;
}

.ai-input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: 8px;
    background: #f8f9fa;
    border-radius: 25px;
    padding: 8px 12px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.ai-input-wrapper:focus-within {
    border-color: #667eea;
    background: white;
}

.ai-text-input {
    flex: 1;
    border: none;
    background: transparent;
    padding: 8px 0;
    font-size: 14px;
    resize: none;
    outline: none;
    max-height: 100px;
    min-height: 20px;
}

.ai-input-actions {
    display: flex;
    gap: 4px;
}

.ai-action-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    color: #666;
}

.ai-action-btn:hover {
    background: #e9ecef;
    color: #333;
}

.ai-action-btn.active {
    background: #667eea;
    color: white;
}

.ai-action-btn svg {
    width: 16px;
    height: 16px;
}

/* File Upload */
.ai-file-input {
    display: none;
}

.ai-file-preview {
    display: none;
    margin-top: 8px;
    padding: 8px;
    background: #e3f2fd;
    border-radius: 8px;
    font-size: 12px;
    color: #1976d2;
}

/* Voice Recording */
.ai-voice-recording {
    display: none;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
    padding: 8px;
    background: #ffebee;
    border-radius: 8px;
    font-size: 12px;
    color: #c62828;
}

.ai-voice-wave {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #f44336;
    animation: voiceWave 1s infinite;
}

@keyframes voiceWave {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

/* Quick Actions */
.ai-quick-actions {
    display: flex;
    gap: 8px;
    margin-top: 12px;
    flex-wrap: wrap;
}

.ai-quick-btn {
    padding: 6px 12px;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 15px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #666;
}

.ai-quick-btn:hover {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

/* Responsive Design */
@media (max-width: 480px) {
    .ai-chat-widget {
        width: calc(100vw - 40px);
        height: calc(100vh - 100px);
        bottom: 80px;
        right: 20px;
        left: 20px;
    }
    
    .ai-widget-container {
        bottom: 20px;
        right: 20px;
    }
}

/* Accessibility */
.ai-widget-button:focus,
.ai-action-btn:focus,
.ai-quick-btn:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .ai-chat-widget {
        background: #2c2c2c;
        color: white;
    }
    
    .ai-messages-container {
        background: #1e1e1e;
    }
    
    .ai-message.bot .ai-message-bubble {
        background: #3c3c3c;
        color: white;
        border-color: #555;
    }
    
    .ai-input-wrapper {
        background: #3c3c3c;
    }
    
    .ai-input-wrapper:focus-within {
        background: #2c2c2c;
    }
}
