"""
Navigation-based Actions for Eaglora HRMS Rasa Bot

These actions provide navigation responses that redirect users to specific pages
in the HRMS system instead of providing data directly in the chat.
"""

import requests
import json
from typing import Any, Text, Dict, List
from datetime import datetime, date
import logging

from rasa_sdk import Action, Tracker
from rasa_sdk.executor import CollectingDispatcher
from rasa_sdk.events import SlotSet, FollowupAction

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# HRMS Navigation URLs
HRMS_PAGES = {
    # Employee Management
    "employee_profile": "/employee/profile",
    "employee_search": "/employee/search",
    "employee_list": "/employee/list",
    "update_profile": "/employee/profile/edit",
    
    # Attendance Management
    "attendance_view": "/attendance/view",
    "attendance_report": "/attendance/report",
    "clock_in": "/attendance/clock-in",
    "clock_out": "/attendance/clock-out",
    "timesheet": "/attendance/timesheet",
    "work_schedule": "/attendance/schedule",
    
    # Leave Management
    "leave_apply": "/leave/apply",
    "leave_balance": "/leave/balance",
    "leave_requests": "/leave/requests",
    "leave_history": "/leave/history",
    "leave_calendar": "/leave/calendar",
    "leave_approval": "/leave/approval",
    
    # Payroll Management
    "payroll_view": "/payroll/view",
    "salary_details": "/payroll/salary",
    "payslip_download": "/payroll/payslip",
    "tax_info": "/payroll/tax",
    "expense_claims": "/payroll/expenses",
    
    # Asset Management
    "asset_request": "/assets/request",
    "asset_status": "/assets/status",
    "asset_return": "/assets/return",
    "asset_list": "/assets/list",
    
    # Help Desk
    "help_desk": "/helpdesk/tickets",
    "create_ticket": "/helpdesk/create",
    "ticket_status": "/helpdesk/status",
    
    # Company Information
    "company_info": "/company/info",
    "department_info": "/company/departments",
    "holidays": "/company/holidays",
    "announcements": "/company/announcements",
    "policies": "/company/policies",
    "org_chart": "/company/org-chart",
    
    # Team Management
    "team_info": "/team/info",
    "manager_info": "/team/manager",
    "subordinates": "/team/subordinates",
    
    # Training & Development
    "training_programs": "/training/programs",
    "training_enrollment": "/training/enroll",
    "training_status": "/training/status",
    "performance_review": "/performance/review",
    
    # Benefits & Personal
    "benefits_info": "/benefits/info",
    "emergency_contacts": "/profile/emergency-contacts",
    "bank_details": "/profile/bank-details",
    
    # Meeting & Calendar
    "calendar": "/calendar/view",
    "meeting_rooms": "/meetings/rooms",
    "book_room": "/meetings/book",
    
    # Notifications
    "notifications": "/notifications/view",
    "settings": "/settings/preferences"
}

class NavigationActionBase(Action):
    """Base class for navigation actions"""

    def name(self) -> Text:
        return "navigation_action_base"

    def create_navigation_response(self,
                                 page_url: str,
                                 message: str,
                                 filters: Dict = None,
                                 action_type: str = "navigate") -> Dict[str, Any]:
        """Create a standardized navigation response"""
        return {
            "type": "navigation",
            "action": action_type,
            "url": page_url,
            "filters": filters or {},
            "message": message,
            "timestamp": datetime.now().isoformat(),
            "confidence": 0.95
        }

class ActionNavigateToAttendance(Action):
    """Navigate to attendance page"""
    
    def name(self) -> Text:
        return "action_check_attendance"
    
    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        
        # Get user parameters
        date = tracker.get_slot("date")
        time_period = tracker.get_slot("time_period")
        
        # Build filters
        filters = {}
        message = "Let me show you your attendance information."
        
        if date:
            filters["date"] = date
            message = f"I'll show you your attendance for {date}."
        elif time_period:
            filters["period"] = time_period
            message = f"I'll show you your attendance for {time_period}."
        
        # Send navigation response
        navigation_response = self.create_navigation_response(
            page_url=HRMS_PAGES["attendance_view"],
            message=message,
            filters=filters
        )
        
        dispatcher.utter_message(
            text=message,
            json_message=navigation_response
        )
        
        return []
    
    def create_navigation_response(self, page_url: str, message: str, filters: Dict = None) -> Dict[str, Any]:
        return {
            "type": "navigation",
            "action": "navigate",
            "url": page_url,
            "filters": filters or {},
            "message": message,
            "confidence": 0.95
        }

class ActionNavigateToLeaveBalance(Action):
    """Navigate to leave balance page"""
    
    def name(self) -> Text:
        return "action_check_leave_balance"
    
    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        
        leave_type = tracker.get_slot("leave_type")
        
        filters = {}
        message = "Let me show you your leave balance information."
        
        if leave_type:
            filters["leave_type"] = leave_type
            message = f"I'll show you your {leave_type} balance."
        
        navigation_response = {
            "type": "navigation",
            "action": "navigate",
            "url": HRMS_PAGES["leave_balance"],
            "filters": filters,
            "message": message,
            "confidence": 0.95
        }
        
        dispatcher.utter_message(
            text=message,
            json_message=navigation_response
        )
        
        return []

class ActionNavigateToLeaveApplication(Action):
    """Navigate to leave application page"""
    
    def name(self) -> Text:
        return "action_apply_leave"
    
    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        
        leave_type = tracker.get_slot("leave_type")
        start_date = tracker.get_slot("start_date")
        end_date = tracker.get_slot("end_date")
        duration = tracker.get_slot("duration")
        
        filters = {}
        message = "I'll help you apply for leave. Let me take you to the leave application page."
        
        # Pre-fill form data if available
        if leave_type:
            filters["leave_type"] = leave_type
        if start_date:
            filters["start_date"] = start_date
        if end_date:
            filters["end_date"] = end_date
        if duration:
            filters["duration"] = duration
        
        if filters:
            message = "I'll take you to the leave application page with your details pre-filled."
        
        navigation_response = {
            "type": "navigation",
            "action": "navigate",
            "url": HRMS_PAGES["leave_apply"],
            "filters": filters,
            "message": message,
            "pre_fill": True,
            "confidence": 0.95
        }
        
        dispatcher.utter_message(
            text=message,
            json_message=navigation_response
        )
        
        return []

class ActionNavigateToEmployeeProfile(Action):
    """Navigate to employee profile page"""
    
    def name(self) -> Text:
        return "action_get_employee_profile"
    
    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        
        employee_name = tracker.get_slot("employee_name")
        badge_id = tracker.get_slot("badge_id")
        email = tracker.get_slot("email")
        
        filters = {}
        message = "Let me show you the employee profile."
        
        if employee_name:
            filters["employee_name"] = employee_name
            message = f"I'll show you {employee_name}'s profile."
        elif badge_id:
            filters["badge_id"] = badge_id
            message = f"I'll show you the profile for employee {badge_id}."
        elif email:
            filters["email"] = email
            message = f"I'll show you the profile for {email}."
        else:
            message = "I'll show you your profile."
        
        navigation_response = {
            "type": "navigation",
            "action": "navigate",
            "url": HRMS_PAGES["employee_profile"],
            "filters": filters,
            "message": message,
            "confidence": 0.95
        }
        
        dispatcher.utter_message(
            text=message,
            json_message=navigation_response
        )
        
        return []

class ActionNavigateToPayroll(Action):
    """Navigate to payroll page"""
    
    def name(self) -> Text:
        return "action_get_payroll_info"
    
    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        
        month = tracker.get_slot("month")
        year = tracker.get_slot("year")
        
        filters = {}
        message = "Let me show you your payroll information."
        
        if month and year:
            filters["month"] = month
            filters["year"] = year
            message = f"I'll show you your payroll for {month} {year}."
        elif month:
            filters["month"] = month
            message = f"I'll show you your payroll for {month}."
        
        navigation_response = {
            "type": "navigation",
            "action": "navigate",
            "url": HRMS_PAGES["payroll_view"],
            "filters": filters,
            "message": message,
            "confidence": 0.95
        }
        
        dispatcher.utter_message(
            text=message,
            json_message=navigation_response
        )
        
        return []

class ActionNavigateToAssetRequest(Action):
    """Navigate to asset request page"""
    
    def name(self) -> Text:
        return "action_asset_request"
    
    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        
        asset_type = tracker.get_slot("asset_type")
        
        filters = {}
        message = "I'll help you request an asset. Let me take you to the asset request page."
        
        if asset_type:
            filters["asset_type"] = asset_type
            message = f"I'll help you request a {asset_type}. Taking you to the asset request page."
        
        navigation_response = {
            "type": "navigation",
            "action": "navigate",
            "url": HRMS_PAGES["asset_request"],
            "filters": filters,
            "message": message,
            "pre_fill": True,
            "confidence": 0.95
        }
        
        dispatcher.utter_message(
            text=message,
            json_message=navigation_response
        )
        
        return []

class ActionNavigateToHelpDesk(Action):
    """Navigate to help desk page"""
    
    def name(self) -> Text:
        return "action_help_desk_ticket"
    
    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        
        description = tracker.get_slot("description")
        priority = tracker.get_slot("priority")
        category = tracker.get_slot("category")
        
        filters = {}
        message = "I'll help you create a support ticket. Let me take you to the help desk."
        
        if description:
            filters["description"] = description
        if priority:
            filters["priority"] = priority
        if category:
            filters["category"] = category
        
        if filters:
            message = "I'll take you to the help desk with your issue details pre-filled."
        
        navigation_response = {
            "type": "navigation",
            "action": "navigate",
            "url": HRMS_PAGES["create_ticket"],
            "filters": filters,
            "message": message,
            "pre_fill": True,
            "confidence": 0.95
        }
        
        dispatcher.utter_message(
            text=message,
            json_message=navigation_response
        )
        
        return []

class ActionNavigateToCompanyInfo(Action):
    """Navigate to company information page"""
    
    def name(self) -> Text:
        return "action_get_company_info"
    
    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        
        message = "Let me show you the company information page."
        
        navigation_response = {
            "type": "navigation",
            "action": "navigate",
            "url": HRMS_PAGES["company_info"],
            "filters": {},
            "message": message,
            "confidence": 0.95
        }
        
        dispatcher.utter_message(
            text=message,
            json_message=navigation_response
        )
        
        return []

class ActionNavigateToHolidays(Action):
    """Navigate to holidays page"""
    
    def name(self) -> Text:
        return "action_get_holidays"
    
    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        
        year = tracker.get_slot("year")
        
        filters = {}
        message = "Let me show you the company holidays."
        
        if year:
            filters["year"] = year
            message = f"I'll show you the holidays for {year}."
        
        navigation_response = {
            "type": "navigation",
            "action": "navigate",
            "url": HRMS_PAGES["holidays"],
            "filters": filters,
            "message": message,
            "confidence": 0.95
        }
        
        dispatcher.utter_message(
            text=message,
            json_message=navigation_response
        )
        
        return []
