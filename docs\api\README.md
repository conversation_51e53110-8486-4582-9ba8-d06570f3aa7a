# Eaglora HRMS - Employee Module API Documentation

## 📋 Overview

This directory contains comprehensive API documentation for the Employee Module of the Eaglora HRMS system. The documentation includes detailed endpoint specifications, examples, and testing tools.

## 📁 Files

### 📖 Documentation Files

1. **[employee_module_api_documentation.md](employee_module_api_documentation.md)**
   - Complete API documentation with all endpoints
   - Request/response examples
   - Authentication details
   - Error handling
   - Data models and validation rules
   - SDK examples in Python and JavaScript
   - Performance considerations and security best practices

2. **[employee_api_quick_reference.md](employee_api_quick_reference.md)**
   - Quick reference guide for developers
   - Endpoint summary table
   - Common query parameters
   - Sample curl commands
   - HTTP status codes

### 🧪 Testing Tools

3. **[test_employee_api.py](test_employee_api.py)**
   - Automated test script to validate API endpoints
   - Validates response formats and required fields
   - Tests authentication, CRUD operations, search, and filtering
   - Can be run against any Eaglora instance

## 🚀 Quick Start

### Prerequisites
- Eaglora HRMS system running
- Admin user credentials
- Python 3.6+ (for testing script)

### Testing the API

1. **Run the test script:**
   ```bash
   cd docs/api
   python test_employee_api.py
   ```

2. **Test against different environment:**
   ```bash
   python test_employee_api.py https://your-eaglora-instance.com
   ```

### Basic API Usage

1. **Get JWT Token:**
   ```bash
   curl -X POST "http://localhost:8000/api/auth/login/" \
     -H "Content-Type: application/json" \
     -d '{"username": "admin", "password": "admin"}'
   ```

2. **List Employees:**
   ```bash
   curl -X GET "http://localhost:8000/api/employee/employees/" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN"
   ```

## 📊 API Test Results

The API documentation has been validated with automated tests:

✅ **Authentication** - JWT token-based authentication working  
✅ **Employee CRUD** - Create, Read, Update, Delete operations  
✅ **Search & Filtering** - Advanced search and filtering capabilities  
✅ **Pagination** - Proper pagination support  
✅ **Error Handling** - Appropriate HTTP status codes  
✅ **Data Validation** - Request/response format validation  

## 🔧 API Endpoints Summary

### Core Employee Operations
- `GET /api/employee/employees/` - List all employees
- `GET /api/employee/employees/{id}/` - Get employee details
- `POST /api/employee/employees/` - Create new employee
- `PUT /api/employee/employees/{id}/` - Update employee
- `DELETE /api/employee/employees/{id}/` - Delete employee

### Employee Work Information
- `GET /api/employee/employee-work-information/` - List work info
- `POST /api/employee/employee-work-information/` - Create work info
- `PUT /api/employee/employee-work-information/{id}/` - Update work info

### Search & Filtering
- `GET /api/employee/list/employees/` - Advanced search
- `GET /api/employee/employee-selector/` - Hierarchy-based selection

### Bulk Operations
- `PUT /api/employee/employee-bulk-update/` - Bulk update
- `GET /api/employee/employee-work-info-export/` - Export data
- `POST /api/employee/employee-work-info-import/` - Import data

## 🔐 Authentication

The API uses JWT (JSON Web Token) authentication:

1. **Login** to get access token
2. **Include token** in Authorization header: `Bearer YOUR_TOKEN`
3. **Token expires** after 30 days (configurable)

## 🎯 Key Features

### ✨ Advanced Capabilities
- **Hierarchical Access Control** - Role-based permissions
- **Advanced Search** - Multi-field search with filters
- **Bulk Operations** - Import/export and bulk updates
- **Audit Trail** - All changes are logged
- **Data Validation** - Comprehensive input validation
- **Pagination** - Efficient handling of large datasets

### 🛡️ Security Features
- JWT authentication
- Permission-based access control
- Input sanitization
- Rate limiting
- HTTPS support

### 📈 Performance Features
- Database query optimization
- Caching support
- Pagination for large datasets
- Selective field loading

## 🔍 Filtering & Search

### Available Filters
- `employee_first_name` - Filter by first name
- `employee_last_name` - Filter by last name
- `email` - Filter by email
- `badge_id` - Filter by badge ID
- `phone` - Filter by phone
- `country` - Filter by country
- `gender` - Filter by gender
- `is_active` - Filter by active status
- `department` - Filter by department

### Search Parameters
- `search` - General search across name fields
- `groupby_field` - Group results by specific field
- `page` - Page number for pagination
- `page_size` - Number of results per page

## 📝 Data Models

### Employee Model
Core employee information including personal details, contact information, and system metadata.

### Employee Work Information
Job-related information including position, department, salary, reporting structure, and employment dates.

### Employee Bank Details
Banking information for payroll processing.

## 🚨 Error Handling

The API returns appropriate HTTP status codes:
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `500` - Internal Server Error

Error responses include detailed error messages and field-specific validation errors.

## 📞 Support

For questions or issues with the API:
1. Check the detailed documentation
2. Run the test script to validate your setup
3. Review the error messages for specific issues
4. Contact the development team

## 📅 Version Information

- **API Version**: 1.0.0
- **Documentation Version**: 1.0.0
- **Last Updated**: January 2024
- **Compatibility**: Eaglora HRMS v2.0+

## 🔄 Updates

This documentation is automatically tested and validated. Any changes to the API will be reflected in updated documentation with corresponding test validations.

---

**Happy coding! 🚀**
