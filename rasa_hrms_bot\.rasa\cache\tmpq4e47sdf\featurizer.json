{"py/object": "rasa.core.featurizers.tracker_featurizers.IntentMaxHistoryTrackerFeaturizer", "state_featurizer": {"py/object": "rasa.core.featurizers.single_state_featurizer.IntentTokenizerSingleStateFeaturizer", "_default_feature_states": {"intent": {"affirm": 0, "apply_leave": 1, "back": 2, "bot_challenge": 3, "check_attendance": 4, "check_leave_balance": 5, "clock_in": 6, "clock_out": 7, "deny": 8, "get_employee_profile": 9, "goodbye": 10, "greet": 11, "nlu_fallback": 12, "out_of_scope": 13, "restart": 14, "session_start": 15}, "action_name": {"action_listen": 0, "action_restart": 1, "action_session_start": 2, "action_default_fallback": 3, "action_deactivate_loop": 4, "action_revert_fallback_events": 5, "action_default_ask_affirmation": 6, "action_default_ask_rephrase": 7, "action_two_stage_fallback": 8, "action_unlikely_intent": 9, "action_back": 10, "...": 11, "action_extract_slots": 12, "action_get_employee_profile": 13, "action_clock_in": 14, "action_clock_out": 15, "action_check_attendance": 16, "action_apply_leave": 17, "action_check_leave_balance": 18, "utter_goodbye": 19, "utter_greet": 20, "utter_iamabot": 21}, "entities": {"employee_id": 0, "employee_name": 1, "end_date": 2, "leave_type": 3, "start_date": 4}, "slots": {"employee_name_0": 0, "employee_id_0": 1}, "active_loop": {}}, "action_texts": [], "entity_tag_specs": []}, "max_history": 5, "remove_duplicates": true}