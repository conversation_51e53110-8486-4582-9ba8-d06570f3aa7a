#!/usr/bin/env python3
"""
Test script for Floating AI Assistant Widget functionality
"""

import os
import sys

def test_widget_files():
    """Test if all widget files exist"""
    print("Testing Floating AI Widget Files")
    print("=" * 40)
    
    required_files = [
        'ai_assistant/static/ai_assistant/css/floating-widget.css',
        'ai_assistant/static/ai_assistant/js/floating-widget.js',
        'ai_assistant/templates/ai_assistant/floating_widget.html',
        'ai_assistant/templates/ai_assistant/floating_widget_demo.html',
        'ai_assistant/templates/ai_assistant/widget_status.html',
        'ai_assistant/templatetags/ai_assistant_tags.py',
    ]
    
    missing_files = []
    existing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            existing_files.append(file_path)
            print(f"✅ {file_path}")
        else:
            missing_files.append(file_path)
            print(f"❌ {file_path}")
    
    print(f"\nSummary: {len(existing_files)} files found, {len(missing_files)} missing")
    
    return len(missing_files) == 0

def test_css_structure():
    """Test CSS file structure"""
    print("\nTesting CSS Structure")
    print("=" * 40)
    
    css_file = 'ai_assistant/static/ai_assistant/css/floating-widget.css'
    
    if not os.path.exists(css_file):
        print("❌ CSS file not found")
        return False
    
    with open(css_file, 'r', encoding='utf-8') as f:
        css_content = f.read()
    
    required_classes = [
        '.ai-widget-container',
        '.ai-widget-button',
        '.ai-chat-widget',
        '.ai-messages-container',
        '.ai-input-container',
        '.ai-message',
        '.ai-typing-indicator',
        '.ai-voice-recording',
        '.ai-file-preview'
    ]
    
    missing_classes = []
    existing_classes = []
    
    for css_class in required_classes:
        if css_class in css_content:
            existing_classes.append(css_class)
            print(f"✅ {css_class}")
        else:
            missing_classes.append(css_class)
            print(f"❌ {css_class}")
    
    print(f"\nCSS Classes: {len(existing_classes)} found, {len(missing_classes)} missing")
    
    return len(missing_classes) == 0

def test_js_structure():
    """Test JavaScript file structure"""
    print("\nTesting JavaScript Structure")
    print("=" * 40)
    
    js_file = 'ai_assistant/static/ai_assistant/js/floating-widget.js'
    
    if not os.path.exists(js_file):
        print("❌ JavaScript file not found")
        return False
    
    with open(js_file, 'r', encoding='utf-8') as f:
        js_content = f.read()
    
    required_methods = [
        'class FloatingAIWidget',
        'toggleWidget',
        'sendMessage',
        'handleFileUpload',
        'toggleVoiceRecording',
        'addMessage',
        'showTyping',
        'hideTyping'
    ]
    
    missing_methods = []
    existing_methods = []
    
    for method in required_methods:
        if method in js_content:
            existing_methods.append(method)
            print(f"✅ {method}")
        else:
            missing_methods.append(method)
            print(f"❌ {method}")
    
    print(f"\nJS Methods: {len(existing_methods)} found, {len(missing_methods)} missing")
    
    return len(missing_methods) == 0

def test_template_structure():
    """Test template file structure"""
    print("\nTesting Template Structure")
    print("=" * 40)
    
    template_file = 'ai_assistant/templates/ai_assistant/floating_widget.html'
    
    if not os.path.exists(template_file):
        print("❌ Template file not found")
        return False
    
    with open(template_file, 'r', encoding='utf-8') as f:
        template_content = f.read()
    
    required_elements = [
        '{% load static %}',
        '{% load ai_assistant_tags %}',
        'floating-widget.css',
        'floating-widget.js',
        'FloatingAIWidget',
        '{% ai_widget_urls %}',
        '{% ai_widget_config %}'
    ]
    
    missing_elements = []
    existing_elements = []
    
    for element in required_elements:
        if element in template_content:
            existing_elements.append(element)
            print(f"✅ {element}")
        else:
            missing_elements.append(element)
            print(f"❌ {element}")
    
    print(f"\nTemplate Elements: {len(existing_elements)} found, {len(missing_elements)} missing")
    
    return len(missing_elements) == 0

def test_widget_features():
    """Test widget feature configuration"""
    print("\nTesting Widget Features")
    print("=" * 40)
    
    js_file = 'ai_assistant/static/ai_assistant/js/floating-widget.js'
    
    if not os.path.exists(js_file):
        print("❌ JavaScript file not found")
        return False
    
    with open(js_file, 'r', encoding='utf-8') as f:
        js_content = f.read()
    
    features = [
        ('Text Input', 'ai-text-input'),
        ('Voice Recording', 'toggleVoiceRecording'),
        ('File Upload', 'handleFileUpload'),
        ('Message History', 'loadChatHistory'),
        ('Navigation', 'handleNavigation'),
        ('Quick Actions', 'ai-quick-btn'),
        ('Typing Indicator', 'showTyping'),
        ('Auto Resize', 'autoResizeTextarea')
    ]
    
    supported_features = []
    missing_features = []
    
    for feature_name, feature_code in features:
        if feature_code in js_content:
            supported_features.append(feature_name)
            print(f"✅ {feature_name}")
        else:
            missing_features.append(feature_name)
            print(f"❌ {feature_name}")
    
    print(f"\nFeatures: {len(supported_features)} supported, {len(missing_features)} missing")
    
    return len(missing_features) == 0

def test_file_size():
    """Test file sizes are reasonable"""
    print("\nTesting File Sizes")
    print("=" * 40)
    
    files_to_check = [
        ('CSS', 'ai_assistant/static/ai_assistant/css/floating-widget.css', 50000),  # 50KB max
        ('JavaScript', 'ai_assistant/static/ai_assistant/js/floating-widget.js', 100000),  # 100KB max
        ('Template', 'ai_assistant/templates/ai_assistant/floating_widget.html', 10000),  # 10KB max
    ]
    
    all_good = True
    
    for file_type, file_path, max_size in files_to_check:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            size_kb = size / 1024
            
            if size <= max_size:
                print(f"✅ {file_type}: {size_kb:.1f} KB (within {max_size/1024:.0f} KB limit)")
            else:
                print(f"⚠️  {file_type}: {size_kb:.1f} KB (exceeds {max_size/1024:.0f} KB limit)")
                all_good = False
        else:
            print(f"❌ {file_type}: File not found")
            all_good = False
    
    return all_good

def main():
    """Run all tests"""
    print("Floating AI Assistant Widget Test Suite")
    print("=" * 50)
    
    tests = [
        ("File Existence", test_widget_files),
        ("CSS Structure", test_css_structure),
        ("JavaScript Structure", test_js_structure),
        ("Template Structure", test_template_structure),
        ("Widget Features", test_widget_features),
        ("File Sizes", test_file_size),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test_name}: Error - {str(e)}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! Floating widget is ready to use.")
        print("\nTo test the widget:")
        print("1. Start your Django server")
        print("2. Visit: /ai-assistant/demo/widget/")
        print("3. Click the floating button in bottom-right corner")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
