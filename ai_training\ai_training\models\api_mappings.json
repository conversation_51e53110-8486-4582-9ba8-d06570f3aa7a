{"auth_login": {"api_endpoint": "http://localhost:8000/api/auth/login/", "method": "POST", "module": "auth", "endpoint": "login", "entity": "authentication", "action": "create", "supports_search": false}, "employee_employees-list": {"api_endpoint": "http://localhost:8000/api/employee/employees/", "method": "GET", "module": "employee", "endpoint": "employees-list", "entity": "employee", "action": "list", "supports_search": false}, "employee_employee-create": {"api_endpoint": "http://localhost:8000/api/employee/employees/", "method": "POST", "module": "employee", "endpoint": "employee-create", "entity": "employee", "action": "create", "supports_search": false}, "employee_employee-detail": {"api_endpoint": "http://localhost:8000/api/employee/employees/{id}/", "method": "GET", "module": "employee", "endpoint": "employee-detail", "entity": "employee", "action": "list", "supports_search": false}, "employee_employee-update": {"api_endpoint": "http://localhost:8000/api/employee/employees/{id}/", "method": "PUT", "module": "employee", "endpoint": "employee-update", "entity": "employee", "action": "update", "supports_search": false}, "employee_employee-delete": {"api_endpoint": "http://localhost:8000/api/employee/employees/{id}/", "method": "DELETE", "module": "employee", "endpoint": "employee-delete", "entity": "employee", "action": "delete", "supports_search": false}, "employee_employee-types-list": {"api_endpoint": "http://localhost:8000/api/employee/employee-type/", "method": "GET", "module": "employee", "endpoint": "employee-types-list", "entity": "employee type", "action": "list", "supports_search": false}, "employee_employee-work-info-list": {"api_endpoint": "http://localhost:8000/api/employee/employee-work-information/", "method": "GET", "module": "employee", "endpoint": "employee-work-info-list", "entity": "work information", "action": "list", "supports_search": false}, "employee_employee-search-advanced": {"api_endpoint": "http://localhost:8000/api/employee/list/employees/", "method": "GET", "module": "employee", "endpoint": "employee-search-advanced", "entity": "employee", "action": "list", "supports_search": false}, "employee_employee-selector": {"api_endpoint": "http://localhost:8000/api/employee/employee-selector/", "method": "GET", "module": "employee", "endpoint": "employee-selector", "entity": "employee", "action": "list", "supports_search": false}, "employee_employee-bulk-update": {"api_endpoint": "http://localhost:8000/api/employee/employee-bulk-update/", "method": "PUT", "module": "employee", "endpoint": "employee-bulk-update", "entity": "employee", "action": "update", "supports_search": false}, "employee_disciplinary-action-list": {"api_endpoint": "http://localhost:8000/api/employee/disciplinary-action/", "method": "GET", "module": "employee", "endpoint": "disciplinary-action-list", "entity": "disciplinary action", "action": "list", "supports_search": false}, "employee_policies-list": {"api_endpoint": "http://localhost:8000/api/employee/policies/", "method": "GET", "module": "employee", "endpoint": "policies-list", "entity": "policy", "action": "list", "supports_search": false}, "employee_documents-list": {"api_endpoint": "http://localhost:8000/api/employee/documents/", "method": "GET", "module": "employee", "endpoint": "documents-list", "entity": "document", "action": "list", "supports_search": false}, "attendance_clock-in": {"api_endpoint": "http://localhost:8000/api/attendance/clock-in/", "method": "POST", "module": "attendance", "endpoint": "clock-in", "entity": "clock in", "action": "clock_in", "supports_search": false}, "attendance_clock-out": {"api_endpoint": "http://localhost:8000/api/attendance/clock-out/", "method": "POST", "module": "attendance", "endpoint": "clock-out", "entity": "clock out", "action": "clock_out", "supports_search": false}, "attendance_attendance-list": {"api_endpoint": "http://localhost:8000/api/attendance/attendance/", "method": "GET", "module": "attendance", "endpoint": "attendance-list", "entity": "attendance", "action": "list", "supports_search": false}, "attendance_attendance-create": {"api_endpoint": "http://localhost:8000/api/attendance/attendance/", "method": "POST", "module": "attendance", "endpoint": "attendance-create", "entity": "attendance", "action": "create", "supports_search": false}, "attendance_attendance-request-list": {"api_endpoint": "http://localhost:8000/api/attendance/attendance-request/", "method": "GET", "module": "attendance", "endpoint": "attendance-request-list", "entity": "attendance request", "action": "list", "supports_search": false}, "attendance_attendance-request-create": {"api_endpoint": "http://localhost:8000/api/attendance/attendance-request/", "method": "POST", "module": "attendance", "endpoint": "attendance-request-create", "entity": "attendance request", "action": "create", "supports_search": false}, "attendance_attendance-request-approve": {"api_endpoint": "http://localhost:8000/api/attendance/attendance-request-approve/{id}/", "method": "POST", "module": "attendance", "endpoint": "attendance-request-approve", "entity": "attendance request", "action": "approve", "supports_search": false}, "attendance_today-attendance": {"api_endpoint": "http://localhost:8000/api/attendance/today-attendance/", "method": "GET", "module": "attendance", "endpoint": "today-attendance", "entity": "today's attendance", "action": "list", "supports_search": false}, "attendance_offline-employees-list": {"api_endpoint": "http://localhost:8000/api/attendance/offline-employees/list/", "method": "GET", "module": "attendance", "endpoint": "offline-employees-list", "entity": "offline employee", "action": "list", "supports_search": false}, "leave_leave-request-list": {"api_endpoint": "http://localhost:8000/api/leave/leave-request/", "method": "GET", "module": "leave", "endpoint": "leave-request-list", "entity": "leave request", "action": "list", "supports_search": false}, "leave_leave-request-create": {"api_endpoint": "http://localhost:8000/api/leave/leave-request/", "method": "POST", "module": "leave", "endpoint": "leave-request-create", "entity": "leave request", "action": "create", "supports_search": false}, "leave_leave-request-approve": {"api_endpoint": "http://localhost:8000/api/leave/leave-request-approve/{id}/", "method": "POST", "module": "leave", "endpoint": "leave-request-approve", "entity": "leave request", "action": "approve", "supports_search": false}, "leave_leave-request-reject": {"api_endpoint": "http://localhost:8000/api/leave/leave-request-reject/{id}/", "method": "POST", "module": "leave", "endpoint": "leave-request-reject", "entity": "leave request", "action": "reject", "supports_search": false}, "leave_leave-type-list": {"api_endpoint": "http://localhost:8000/api/leave/leave-type/", "method": "GET", "module": "leave", "endpoint": "leave-type-list", "entity": "leave type", "action": "list", "supports_search": false}, "leave_leave-allocation-list": {"api_endpoint": "http://localhost:8000/api/leave/leave-allocation/", "method": "GET", "module": "leave", "endpoint": "leave-allocation-list", "entity": "leave allocation", "action": "list", "supports_search": false}, "leave_holiday-list": {"api_endpoint": "http://localhost:8000/api/leave/holiday/", "method": "GET", "module": "leave", "endpoint": "holiday-list", "entity": "holiday", "action": "list", "supports_search": false}, "payroll_contract-list": {"api_endpoint": "http://localhost:8000/api/payroll/contract/", "method": "GET", "module": "payroll", "endpoint": "contract-list", "entity": "contract", "action": "list", "supports_search": false}, "payroll_payslip-list": {"api_endpoint": "http://localhost:8000/api/payroll/payslip/", "method": "GET", "module": "payroll", "endpoint": "payslip-list", "entity": "payslip", "action": "list", "supports_search": false}, "payroll_allowance-list": {"api_endpoint": "http://localhost:8000/api/payroll/allowance/", "method": "GET", "module": "payroll", "endpoint": "allowance-list", "entity": "allowance", "action": "list", "supports_search": false}, "payroll_deduction-list": {"api_endpoint": "http://localhost:8000/api/payroll/deduction/", "method": "GET", "module": "payroll", "endpoint": "deduction-list", "entity": "deduction", "action": "list", "supports_search": false}, "payroll_loan-list": {"api_endpoint": "http://localhost:8000/api/payroll/loan/", "method": "GET", "module": "payroll", "endpoint": "loan-list", "entity": "loan", "action": "list", "supports_search": false}, "payroll_bonus-list": {"api_endpoint": "http://localhost:8000/api/payroll/bonus/", "method": "GET", "module": "payroll", "endpoint": "bonus-list", "entity": "bonus", "action": "list", "supports_search": false}, "asset_asset-list": {"api_endpoint": "http://localhost:8000/api/asset/asset/", "method": "GET", "module": "asset", "endpoint": "asset-list", "entity": "asset", "action": "list", "supports_search": false}, "asset_asset-create": {"api_endpoint": "http://localhost:8000/api/asset/asset/", "method": "POST", "module": "asset", "endpoint": "asset-create", "entity": "asset", "action": "create", "supports_search": false}, "asset_asset-category-list": {"api_endpoint": "http://localhost:8000/api/asset/asset-category/", "method": "GET", "module": "asset", "endpoint": "asset-category-list", "entity": "asset category", "action": "list", "supports_search": false}, "asset_asset-request-list": {"api_endpoint": "http://localhost:8000/api/asset/asset-request/", "method": "GET", "module": "asset", "endpoint": "asset-request-list", "entity": "asset request", "action": "list", "supports_search": false}, "asset_asset-request-approve": {"api_endpoint": "http://localhost:8000/api/asset/asset-request-approve/{id}/", "method": "POST", "module": "asset", "endpoint": "asset-request-approve", "entity": "asset request", "action": "approve", "supports_search": false}, "base_company-list": {"api_endpoint": "http://localhost:8000/api/base/company/", "method": "GET", "module": "base", "endpoint": "company-list", "entity": "company", "action": "list", "supports_search": false}, "base_department-list": {"api_endpoint": "http://localhost:8000/api/base/department/", "method": "GET", "module": "base", "endpoint": "department-list", "entity": "department", "action": "list", "supports_search": false}, "base_job-position-list": {"api_endpoint": "http://localhost:8000/api/base/job-position/", "method": "GET", "module": "base", "endpoint": "job-position-list", "entity": "job position", "action": "list", "supports_search": false}, "base_shift-list": {"api_endpoint": "http://localhost:8000/api/base/shift/", "method": "GET", "module": "base", "endpoint": "shift-list", "entity": "shift", "action": "list", "supports_search": false}, "notifications_notification-list": {"api_endpoint": "http://localhost:8000/api/notifications/notification/", "method": "GET", "module": "notifications", "endpoint": "notification-list", "entity": "notification", "action": "list", "supports_search": false}, "notifications_notification-mark-read": {"api_endpoint": "http://localhost:8000/api/notifications/notification-mark-read/{id}/", "method": "POST", "module": "notifications", "endpoint": "notification-mark-read", "entity": "notification", "action": "create", "supports_search": false}, "ai-assistant_chat-message": {"api_endpoint": "http://localhost:8000/ai-assistant/api/message/", "method": "POST", "module": "ai-assistant", "endpoint": "chat-message", "entity": "message", "action": "create", "supports_search": false}, "ai-assistant_employee-search-ai": {"api_endpoint": "http://localhost:8000/ai-assistant/api/employee/search/", "method": "GET", "module": "ai-assistant", "endpoint": "employee-search-ai", "entity": "employee", "action": "list", "supports_search": false}}