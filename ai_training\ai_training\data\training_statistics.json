{"generation_date": "2025-07-28T02:15:17.619080", "total_modules": 9, "modules": {"auth": {"endpoints": 1, "total_examples": 7854, "average_examples_per_endpoint": 7854, "endpoint_details": [{"name": "login", "examples": 7854}]}, "employee": {"endpoints": 13, "total_examples": 125704, "average_examples_per_endpoint": 9669, "endpoint_details": [{"name": "employees-list", "examples": 10000}, {"name": "employee-create", "examples": 10000}, {"name": "employee-detail", "examples": 10000}, {"name": "employee-update", "examples": 8568}, {"name": "employee-delete", "examples": 8568}, {"name": "employee-types-list", "examples": 10000}, {"name": "employee-work-info-list", "examples": 10000}, {"name": "employee-search-advanced", "examples": 10000}, {"name": "employee-selector", "examples": 10000}, {"name": "employee-bulk-update", "examples": 8568}, {"name": "disciplinary-action-list", "examples": 10000}, {"name": "policies-list", "examples": 10000}, {"name": "documents-list", "examples": 10000}]}, "attendance": {"endpoints": 9, "total_examples": 90000, "average_examples_per_endpoint": 10000, "endpoint_details": [{"name": "clock-in", "examples": 10000}, {"name": "clock-out", "examples": 10000}, {"name": "attendance-list", "examples": 10000}, {"name": "attendance-create", "examples": 10000}, {"name": "attendance-request-list", "examples": 10000}, {"name": "attendance-request-create", "examples": 10000}, {"name": "attendance-request-approve", "examples": 10000}, {"name": "today-attendance", "examples": 10000}, {"name": "offline-employees-list", "examples": 10000}]}, "leave": {"endpoints": 7, "total_examples": 70000, "average_examples_per_endpoint": 10000, "endpoint_details": [{"name": "leave-request-list", "examples": 10000}, {"name": "leave-request-create", "examples": 10000}, {"name": "leave-request-approve", "examples": 10000}, {"name": "leave-request-reject", "examples": 10000}, {"name": "leave-type-list", "examples": 10000}, {"name": "leave-allocation-list", "examples": 10000}, {"name": "holiday-list", "examples": 10000}]}, "payroll": {"endpoints": 6, "total_examples": 60000, "average_examples_per_endpoint": 10000, "endpoint_details": [{"name": "contract-list", "examples": 10000}, {"name": "payslip-list", "examples": 10000}, {"name": "allowance-list", "examples": 10000}, {"name": "deduction-list", "examples": 10000}, {"name": "loan-list", "examples": 10000}, {"name": "bonus-list", "examples": 10000}]}, "asset": {"endpoints": 5, "total_examples": 50000, "average_examples_per_endpoint": 10000, "endpoint_details": [{"name": "asset-list", "examples": 10000}, {"name": "asset-create", "examples": 10000}, {"name": "asset-category-list", "examples": 10000}, {"name": "asset-request-list", "examples": 10000}, {"name": "asset-request-approve", "examples": 10000}]}, "base": {"endpoints": 4, "total_examples": 40000, "average_examples_per_endpoint": 10000, "endpoint_details": [{"name": "company-list", "examples": 10000}, {"name": "department-list", "examples": 10000}, {"name": "job-position-list", "examples": 10000}, {"name": "shift-list", "examples": 10000}]}, "notifications": {"endpoints": 2, "total_examples": 17854, "average_examples_per_endpoint": 8927, "endpoint_details": [{"name": "notification-list", "examples": 10000}, {"name": "notification-mark-read", "examples": 7854}]}, "ai-assistant": {"endpoints": 2, "total_examples": 17854, "average_examples_per_endpoint": 8927, "endpoint_details": [{"name": "chat-message", "examples": 7854}, {"name": "employee-search-ai", "examples": 10000}]}}, "overall_stats": {"total_endpoints": 49, "total_examples": 479266, "average_examples_per_endpoint": 9780}}