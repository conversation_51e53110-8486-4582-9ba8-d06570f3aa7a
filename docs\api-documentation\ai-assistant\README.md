# Ai-Assistant Module API Documentation

## 📋 Overview

The Ai-Assistant module provides comprehensive APIs for managing ai-assistant operations in the Eaglora HRMS system.

## 🔗 Base URL
```
/ai-assistant/api/
```

## 📑 Available Endpoints

| Endpoint | Method | Description | File |
|----------|--------|-------------|------|
| `/message/` | POST | Send message to AI assistant for HRMS queries | [chat-message.md](chat-message.md) |
| `/upload/` | POST | Upload file for AI processing | [file-upload.md](file-upload.md) |
| `/history/` | GET | Get chat conversation history | [chat-history.md](chat-history.md) |
| `/direct-call/` | POST | Direct API call through AI assistant | [direct-api-call.md](direct-api-call.md) |
| `/settings/` | GET/POST | Manage AI assistant settings | [ai-settings.md](ai-settings.md) |
| `/memory/` | GET/POST | Manage conversation memory and context | [conversation-memory.md](conversation-memory.md) |
| `/employee/search/` | GET | AI-powered employee search with natural language | [employee-search-ai.md](employee-search-ai.md) |
| `/leave/apply/` | POST | Quick leave application through AI assistant | [quick-leave-apply.md](quick-leave-apply.md) |
| `/attendance/status/` | GET | Get attendance status through AI assistant | [attendance-status-ai.md](attendance-status-ai.md) |
| `/attendance/clock/` | POST | Clock in/out through AI assistant | [clock-action-ai.md](clock-action-ai.md) |

## 🔐 Authentication & Permissions

All endpoints require JWT authentication:

```http
Authorization: Bearer YOUR_JWT_TOKEN
```

## 🚀 Quick Start

### Basic Request Example
```bash
curl -X GET "/ai-assistant/api/" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

## 📊 Use Cases

This module is commonly used for:
- **AI assistance**
- **AI chat interface**
- **AI clock actions**
- **AI recommendations**
- **API integration**
- **Attendance tracking**
- **Configuration**
- **Context maintenance**
- **Context management**
- **Conversation continuity**
- **Conversation tracking**
- **Direct commands**
- **Document analysis**
- **Employee self-service**
- **Employee support**
- **File processing**
- **HRMS assistance**
- **Hands-free operation**
- **History review**
- **Leave requests**
- **Memory storage**
- **Mobile assistance**
- **Natural language queries**
- **Personalization**
- **Preferences**
- **Quick access**
- **Quick actions**
- **Quick information**
- **Smart search**
- **Status queries**
- **System integration**
- **Voice commands**

## 🔄 Related Modules

- [Employee Module](../employee/README.md)
- [Attendance Module](../attendance/README.md)
- [Leave Module](../leave/README.md)
- [Payroll Module](../payroll/README.md)

---

**Navigate to specific endpoints using the links above.**
