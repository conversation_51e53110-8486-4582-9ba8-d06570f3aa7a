{"models": {"intent_classifier": "intent_classifier.joblib", "search_extractor": "search_extractor.joblib"}, "vectorizers": {"intent": "intent_vectorizer.joblib", "search": "search_vectorizer.joblib"}, "label_encoders": {"intent": "intent_encoder.joblib", "search": "search_encoder.joblib"}, "api_mappings": "api_mappings.json", "training_date": "2025-07-28T02:15:58.077781", "total_training_examples": 479266, "supported_modules": ["auth", "employee", "attendance", "leave", "payroll", "asset", "base", "notifications", "ai-assistant"]}