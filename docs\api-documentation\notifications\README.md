# Notifications Module API Documentation

## 📋 Overview

The Notifications module provides comprehensive APIs for managing notifications operations in the Eaglora HRMS system.

## 🔗 Base URL
```
/api/notifications/
```

## 📑 Available Endpoints

| Endpoint | Method | Description | File |
|----------|--------|-------------|------|
| `/notification/` | GET | List user notifications with filtering | [notification-list.md](notification-list.md) |
| `/notification/` | POST | Create notification | [notification-create.md](notification-create.md) |
| `/notification/{id}/` | GET/PUT | Get or update notification details | [notification-detail.md](notification-detail.md) |
| `/notification-mark-read/{id}/` | POST | Mark notification as read | [notification-mark-read.md](notification-mark-read.md) |
| `/notification-mark-all-read/` | POST | Mark all notifications as read | [notification-mark-all-read.md](notification-mark-all-read.md) |
| `/notification-bulk-delete/` | POST | Bulk delete notifications | [notification-bulk-delete.md](notification-bulk-delete.md) |

## 🔐 Authentication & Permissions

All endpoints require JWT authentication:

```http
Authorization: Bearer YOUR_JWT_TOKEN
```

## 🚀 Quick Start

### Basic Request Example
```bash
curl -X GET "/api/notifications/" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

## 📊 Use Cases

This module is commonly used for:
- **Admin alerts**
- **Bulk operations**
- **Cleanup**
- **Communication**
- **Content management**
- **Notification details**
- **Notification management**
- **Status management**
- **Status tracking**
- **Status updates**
- **System alerts**
- **System notifications**
- **User communication**
- **User convenience**
- **User interaction**
- **User management**
- **User notifications**

## 🔄 Related Modules

- [Employee Module](../employee/README.md)
- [Attendance Module](../attendance/README.md)
- [Leave Module](../leave/README.md)
- [Payroll Module](../payroll/README.md)

---

**Navigate to specific endpoints using the links above.**
