# Asset Module API Documentation

## 📋 Overview

The Asset module provides comprehensive APIs for managing asset operations in the Eaglora HRMS system.

## 🔗 Base URL
```
/api/asset/
```

## 📑 Available Endpoints

| Endpoint | Method | Description | File |
|----------|--------|-------------|------|
| `/asset/` | GET | List company assets with filtering | [asset-list.md](asset-list.md) |
| `/asset/` | POST | Create new asset record | [asset-create.md](asset-create.md) |
| `/asset/{id}/` | GET/PUT | Get or update asset details | [asset-detail.md](asset-detail.md) |
| `/asset-category/` | GET | List asset categories | [asset-category-list.md](asset-category-list.md) |
| `/asset-category/` | POST | Create asset category | [asset-category-create.md](asset-category-create.md) |
| `/asset-category/{id}/` | GET/PUT | Get or update asset category | [asset-category-detail.md](asset-category-detail.md) |
| `/asset-lot/` | GET | List asset lots | [asset-lot-list.md](asset-lot-list.md) |
| `/asset-lot/` | POST | Create asset lot | [asset-lot-create.md](asset-lot-create.md) |
| `/asset-lot/{id}/` | GET/PUT | Get or update asset lot | [asset-lot-detail.md](asset-lot-detail.md) |
| `/asset-allocation/` | GET | List asset allocations | [asset-allocation-list.md](asset-allocation-list.md) |
| `/asset-allocation/` | POST | Create asset allocation | [asset-allocation-create.md](asset-allocation-create.md) |
| `/asset-allocation/{id}/` | GET/PUT | Get or update asset allocation | [asset-allocation-detail.md](asset-allocation-detail.md) |
| `/asset-request/` | GET | List asset requests | [asset-request-list.md](asset-request-list.md) |
| `/asset-request/` | POST | Create asset request | [asset-request-create.md](asset-request-create.md) |
| `/asset-request/{id}/` | GET/PUT | Get or update asset request | [asset-request-detail.md](asset-request-detail.md) |
| `/asset-request-approve/{id}/` | POST | Approve asset request | [asset-request-approve.md](asset-request-approve.md) |
| `/asset-request-reject/{id}/` | POST | Reject asset request | [asset-request-reject.md](asset-request-reject.md) |
| `/asset-return/` | GET | List asset returns | [asset-return-list.md](asset-return-list.md) |
| `/asset-return/` | POST | Create asset return record | [asset-return-create.md](asset-return-create.md) |
| `/asset-return/{id}/` | GET/PUT | Get or update asset return | [asset-return-detail.md](asset-return-detail.md) |

## 🔐 Authentication & Permissions

All endpoints require JWT authentication:

```http
Authorization: Bearer YOUR_JWT_TOKEN
```

## 🚀 Quick Start

### Basic Request Example
```bash
curl -X GET "/api/asset/" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

## 📊 Use Cases

This module is commonly used for:
- **Allocation management**
- **Allocation tracking**
- **Approval workflow**
- **Asset allocation**
- **Asset assignment**
- **Asset classification**
- **Asset details**
- **Asset needs**
- **Asset recovery**
- **Asset registration**
- **Asset return**
- **Asset tracking**
- **Assignment history**
- **Assignment updates**
- **Batch management**
- **Batch processing**
- **Batch updates**
- **Bulk asset management**
- **Category management**
- **Category setup**
- **Classification updates**
- **Distribution**
- **Employee allocation**
- **Employee assets**
- **Employee departure**
- **Employee departures**
- **Employee requests**
- **Information updates**
- **Inventory addition**
- **Inventory control**
- **Inventory management**
- **Inventory organization**
- **Inventory tracking**
- **Lot creation**
- **Lot management**
- **Lot tracking**
- **Maintenance records**
- **Manager actions**
- **Manager decisions**
- **New purchases**
- **Organization**
- **Processing**
- **Reporting**
- **Request approval**
- **Request details**
- **Request management**
- **Request rejection**
- **Return management**
- **Return tracking**
- **Status updates**
- **Transfer tracking**
- **Workflow management**
- **Workflow processing**

## 🔄 Related Modules

- [Employee Module](../employee/README.md)
- [Attendance Module](../attendance/README.md)
- [Leave Module](../leave/README.md)
- [Payroll Module](../payroll/README.md)

---

**Navigate to specific endpoints using the links above.**
