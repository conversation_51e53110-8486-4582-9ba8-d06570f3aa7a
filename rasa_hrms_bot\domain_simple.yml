version: "3.1"

intents:
  - greet
  - goodbye
  - affirm
  - deny
  - bot_challenge
  - get_employee_profile
  - clock_in
  - clock_out
  - check_attendance
  - apply_leave
  - check_leave_balance

entities:
  - employee_name
  - employee_id
  - leave_type
  - start_date
  - end_date

slots:
  employee_name:
    type: text
    influence_conversation: true
    mappings:
    - type: from_entity
      entity: employee_name
  
  employee_id:
    type: text
    influence_conversation: true
    mappings:
    - type: from_entity
      entity: employee_id

responses:
  utter_greet:
  - text: "Hello! I'm your HR assistant. How can I help you today?"
  
  utter_goodbye:
  - text: "Goodbye! Have a great day!"
  
  utter_iamabot:
  - text: "I am a bot, powered by <PERSON><PERSON>."

actions:
  - action_get_employee_profile
  - action_clock_in
  - action_clock_out
  - action_check_attendance
  - action_apply_leave
  - action_check_leave_balance

session_config:
  session_expiration_time: 60
  carry_over_slots_to_new_session: true
