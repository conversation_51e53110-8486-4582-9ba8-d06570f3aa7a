"""
Rasa Integration Module for HRMS AI Assistant
Handles communication with Rasa server and manages conversation flow
"""

import json
import logging
import requests
from typing import Dict, List, Any, Optional
from django.conf import settings
from django.contrib.auth.models import User
from .models import AIAssistantSettings, ChatHistory, ConversationMemory

logger = logging.getLogger(__name__)


class RasaIntegration:
    """
    Main class for integrating with Rasa server
    """
    
    def __init__(self):
        self.settings = self._get_settings()
        self.base_url = self.settings.rasa_server_url.rstrip('/')
        
    def _get_settings(self) -> AIAssistantSettings:
        """Get AI Assistant settings"""
        settings_obj = AIAssistantSettings.objects.filter(is_active=True).first()
        if not settings_obj:
            # Create default settings if none exist
            settings_obj = AIAssistantSettings.objects.create()
        return settings_obj
    
    def send_message(self, message: str, sender_id: str, user: User) -> Dict[str, Any]:
        """
        Send message to <PERSON><PERSON> and get response

        Args:
            message: User's message
            sender_id: Unique sender identifier
            user: Django user object

        Returns:
            Dictionary containing response data
        """
        # Check if <PERSON><PERSON> is enabled in settings
        try:
            from .models import AIAssistantSettings
            settings = AIAssistantSettings.objects.filter(is_active=True).first()
            if settings and not settings.enable_rasa:
                logger.info("Rasa is disabled in settings, using mock response")
                return self._create_mock_response(message, user, sender_id)
        except Exception as e:
            logger.warning(f"Could not check AI settings: {str(e)}")

        try:
            # Prepare request payload
            payload = {
                "sender": sender_id,
                "message": message,
                "metadata": {
                    "user_id": user.id,
                    "username": user.username,
                    "employee_id": getattr(user, 'employee_get', {}).id if hasattr(user, 'employee_get') else None
                }
            }
            
            # Send to Rasa
            response = requests.post(
                f"{self.base_url}/webhooks/rest/webhook",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                rasa_response = response.json()
                return self._process_rasa_response(rasa_response, message, user, sender_id)
            else:
                logger.error(f"Rasa server error: {response.status_code} - {response.text}")
                return self._create_error_response("Rasa server error")
                
        except requests.exceptions.RequestException as e:
            logger.info(f"Rasa server not available, using fallback response: {str(e)}")
            # Fallback to mock response for testing
            return self._create_mock_response(message, user, sender_id)
        except Exception as e:
            logger.error(f"Unexpected error in Rasa integration: {str(e)}")
            return self._create_error_response("Unexpected error occurred")
    
    def _process_rasa_response(self, rasa_response: List[Dict], 
                              original_message: str, user: User, sender_id: str) -> Dict[str, Any]:
        """
        Process Rasa response and format for frontend
        
        Args:
            rasa_response: Response from Rasa server
            original_message: Original user message
            user: Django user object
            sender_id: Sender identifier
            
        Returns:
            Formatted response dictionary
        """
        if not rasa_response:
            return self._create_error_response("No response from AI assistant")
        
        # Extract intent and confidence from first response
        intent = None
        confidence = 0.0
        
        # Combine all text responses
        text_responses = []
        buttons = []
        custom_data = {}
        
        for response_item in rasa_response:
            if 'text' in response_item:
                text_responses.append(response_item['text'])
            if 'buttons' in response_item:
                buttons.extend(response_item['buttons'])
            if 'custom' in response_item:
                custom_data.update(response_item['custom'])
        
        # Create formatted response
        formatted_response = {
            "action": custom_data.get('action', 'message'),
            "url": custom_data.get('url', ''),
            "filters": custom_data.get('filters', {}),
            "message": ' '.join(text_responses) if text_responses else "I understand your request.",
            "buttons": buttons,
            "confidence": confidence,
            "intent": intent,
            "api_calls": custom_data.get('api_calls', [])
        }
        
        # Save to chat history
        self._save_chat_history(
            user=user,
            message=original_message,
            response=formatted_response,
            intent=intent,
            confidence=confidence,
            session_id=sender_id
        )
        
        return formatted_response
    
    def _create_error_response(self, error_message: str) -> Dict[str, Any]:
        """Create standardized error response"""
        return {
            "action": "error",
            "url": "",
            "filters": {},
            "message": error_message,
            "buttons": [],
            "confidence": 0.0,
            "intent": "error",
            "api_calls": []
        }
    
    def _save_chat_history(self, user: User, message: str, response: Dict[str, Any],
                          intent: str, confidence: float, session_id: str):
        """Save chat interaction to database"""
        try:
            ChatHistory.objects.create(
                user=user,
                message=message,
                response=response,
                intent=intent,
                confidence=confidence,
                session_id=session_id,
                api_calls_made=response.get('api_calls', [])
            )
        except Exception as e:
            logger.error(f"Failed to save chat history: {str(e)}")
    
    def get_conversation_memory(self, user: User, session_id: str) -> Optional[ConversationMemory]:
        """Get conversation memory for user session"""
        try:
            return ConversationMemory.objects.filter(
                user=user,
                session_id=session_id,
                expires_at__gt=timezone.now()
            ).first()
        except Exception as e:
            logger.error(f"Failed to get conversation memory: {str(e)}")
            return None
    
    def update_conversation_memory(self, user: User, session_id: str, context_data: Dict[str, Any]):
        """Update conversation memory with new context"""
        try:
            memory, created = ConversationMemory.objects.get_or_create(
                user=user,
                session_id=session_id,
                defaults={
                    'context_data': context_data,
                    'expires_at': timezone.now() + timedelta(hours=24)
                }
            )
            if not created:
                memory.context_data.update(context_data)
                memory.expires_at = timezone.now() + timedelta(hours=24)
                memory.save()
        except Exception as e:
            logger.error(f"Failed to update conversation memory: {str(e)}")
    
    def train_rasa_model(self) -> bool:
        """
        Trigger Rasa model training
        
        Returns:
            True if training started successfully, False otherwise
        """
        try:
            response = requests.post(
                f"{self.base_url}/model/train",
                timeout=60
            )
            return response.status_code == 200
        except Exception as e:
            logger.error(f"Failed to trigger Rasa training: {str(e)}")
            return False
    
    def get_rasa_status(self) -> Dict[str, Any]:
        """
        Get Rasa server status
        
        Returns:
            Dictionary containing server status information
        """
        try:
            response = requests.get(f"{self.base_url}/status", timeout=10)
            if response.status_code == 200:
                return response.json()
            else:
                return {"status": "error", "message": "Server not responding"}
        except Exception as e:
            logger.error(f"Failed to get Rasa status: {str(e)}")
            return {"status": "error", "message": str(e)}

    def _create_mock_response(self, message: str, user: User, sender_id: str) -> Dict[str, Any]:
        """
        Create a mock response when Rasa is not available
        This provides high-confidence responses for testing
        """
        message_lower = message.lower()

        # Handle anonymous user
        user_name = "there"
        if user and hasattr(user, 'first_name') and user.first_name:
            user_name = user.first_name
        elif user and hasattr(user, 'username') and user.username:
            user_name = user.username

        # Mock intent detection with high confidence
        if any(word in message_lower for word in ['profile', 'employee', 'information', 'details']):
            intent = 'get_employee_profile'
            # Extract employee name from message for better search
            employee_name = self._extract_employee_name(message)
            if employee_name:
                # Try to find specific employee and navigate to their profile
                navigation_result = self._handle_employee_profile_navigation(employee_name, user)
                response_text = navigation_result['message']
                action = navigation_result['action']
                url = navigation_result['url']
                filters = navigation_result['filters']
            else:
                # Generic employee search
                response_text = f"I can help you with employee profile information. Let me search for employees."
                action = 'navigate'
                url = '/employee/employee-view/'
                filters = {'search': message}

        elif any(word in message_lower for word in ['balance', 'remaining', 'available']):
            intent = 'check_leave_balance'
            response_text = "Navigating to your leave balance information."
            action = 'navigate'
            url = '/leave/user-leave-request/'
            filters = {}

        elif any(word in message_lower for word in ['leave', 'vacation', 'time off', 'apply']):
            intent = 'apply_leave'
            response_text = "Navigating to the leave application form."
            action = 'navigate'
            url = '/leave/user-request-create/'
            filters = {}

        elif any(word in message_lower for word in ['attendance', 'clock', 'time', 'present']):
            intent = 'check_attendance'
            response_text = "Navigating to your attendance information."
            action = 'navigate'
            url = '/attendance/attendance-view/'
            filters = {}

        elif any(word in message_lower for word in ['hello', 'hi', 'hey', 'greet']):
            intent = 'greet'
            response_text = f"Hello {user_name}! I'm your AI HR assistant. How can I help you today?"
            action = 'message'
            url = ''
            filters = {}

        else:
            intent = 'general_query'
            response_text = "I understand you're asking about HR-related matters. Could you please be more specific about what you need help with?"
            action = 'message'
            url = ''
            filters = {}

        # Create high-confidence response
        response = {
            'action': action,
            'url': url,
            'filters': filters,
            'message': response_text,
            'buttons': self._get_quick_action_buttons(),
            'confidence': 0.95,  # High confidence for mock responses
            'intent': intent,
            'api_calls': []
        }

        # Save to chat history (only if user is authenticated)
        if user and user.is_authenticated:
            self._save_chat_history(user, message, response, intent, 0.95, sender_id)

        return response

    def _extract_employee_name(self, message: str) -> str:
        """
        Extract employee name from the message

        Args:
            message: User message

        Returns:
            Extracted employee name or empty string
        """
        import re

        # Common patterns for employee name extraction
        patterns = [
            r'(?:get|show|find)\s+([a-zA-Z\s]+)\'?s?\s+(?:profile|information|details)',  # "get sethu's profile"
            r'(?:profile|information|details)\s+(?:of|for)\s+([a-zA-Z\s]+)',  # "profile of john doe"
            r'(?:get|show|find)\s+([a-zA-Z\s]+)(?:\s+profile|\s+information|\s+details)',  # "get john doe profile"
            r'([a-zA-Z\s]+)(?:\s+profile|\s+information|\s+details)',  # "john doe profile"
            r'(?:employee|staff)\s+([a-zA-Z\s]+)',  # "employee john doe"
        ]

        message_lower = message.lower()
        for pattern in patterns:
            match = re.search(pattern, message_lower)
            if match:
                name = match.group(1).strip()
                # Filter out common words that aren't names
                exclude_words = {'profile', 'information', 'details', 'employee', 'staff', 'get', 'show', 'find', 'of', 'for', 'the', 'a', 'an', 'my', 'me', 'i'}
                name_words = [word for word in name.split() if word.lower() not in exclude_words]
                if name_words and len(' '.join(name_words)) >= 1:  # Allow single names
                    return ' '.join(name_words).title()

        return ""

    def _handle_employee_profile_navigation(self, employee_name: str, user: User) -> Dict[str, Any]:
        """
        Handle navigation to employee profile based on search

        Args:
            employee_name: Name to search for
            user: Current user

        Returns:
            Dictionary with navigation details
        """
        try:
            # Try to import and use Django models
            from employee.models import Employee
            from django.db.models import Q

            # Search for employees matching the name
            employees = Employee.objects.filter(
                Q(employee_first_name__icontains=employee_name) |
                Q(employee_last_name__icontains=employee_name) |
                Q(employee_first_name__icontains=employee_name.split()[0]) |
                (Q(employee_last_name__icontains=employee_name.split()[-1]) if len(employee_name.split()) > 1 else Q())
            ).filter(is_active=True)

            if employees.count() == 1:
                # Single employee found - navigate directly to their profile
                employee = employees.first()
                return {
                    'message': f"Navigating to {employee.get_full_name()}'s profile.",
                    'action': 'navigate',
                    'url': f'/employee/employee-view/{employee.id}/',
                    'filters': {}
                }
            elif employees.count() > 1:
                # Multiple employees found - navigate to search results
                employee_names = [emp.get_full_name() for emp in employees[:3]]
                names_text = ', '.join(employee_names)
                if employees.count() > 3:
                    names_text += f" and {employees.count() - 3} more"

                return {
                    'message': f"Found multiple employees matching '{employee_name}': {names_text}. Showing search results.",
                    'action': 'navigate',
                    'url': '/employee/employee-view/',
                    'filters': {'search': employee_name}
                }
            else:
                # No employees found
                return {
                    'message': f"No employee found with the name '{employee_name}'. Showing all employees.",
                    'action': 'navigate',
                    'url': '/employee/employee-view/',
                    'filters': {'search': employee_name}
                }

        except Exception as e:
            logger.error(f"Error in employee profile navigation: {str(e)}")
            # Fallback response when database is not available
            return {
                'message': f"Navigating to employee search for '{employee_name}'.",
                'action': 'navigate',
                'url': '/employee/employee-view/',
                'filters': {'search': employee_name}
            }

    def _get_quick_action_buttons(self) -> List[Dict[str, str]]:
        """Get quick action buttons for the response"""
        return [
            {'text': 'Check My Profile', 'action': 'get_profile'},
            {'text': 'Apply for Leave', 'action': 'apply_leave'},
            {'text': 'Check Attendance', 'action': 'check_attendance'},
            {'text': 'Leave Balance', 'action': 'leave_balance'}
        ]
