{% load static %}
<!-- Simple Floating AI Assistant Widget (No Template Tags) -->

<!-- CSS -->
<link rel="stylesheet" href="{% static 'ai_assistant/css/floating-widget.css' %}">

<!-- Widget will be dynamically created by JavaScript -->

<!-- JavaScript -->
<script src="{% static 'ai_assistant/js/floating-widget.js' %}"></script>

<!-- Simple Configuration -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Simple widget initialization without template tags
    if (typeof FloatingAIWidget !== 'undefined') {
        window.aiWidget = new FloatingAIWidget({
            apiUrl: '/ai-assistant/api/message/',
            uploadUrl: '/ai-assistant/api/upload/',
            enableVoice: true,
            enableFileUpload: true,
            maxFileSize: 10 * 1024 * 1024, // 10MB
            allowedFileTypes: [
                'image/*', 
                'application/pdf', 
                '.doc', 
                '.docx', 
                '.txt', 
                '.csv'
            ],
            theme: 'light',
            position: 'bottom-right'
        });
        
        console.log('AI Assistant Widget loaded successfully');
    } else {
        console.error('FloatingAIWidget class not found');
    }
});
</script>

<!-- CSRF Token for AJAX requests -->
{% csrf_token %}

<!-- Debug info -->
<script>
console.log('Floating widget template loaded');
console.log('Static URL:', '{% static "ai_assistant/css/floating-widget.css" %}');
</script>
