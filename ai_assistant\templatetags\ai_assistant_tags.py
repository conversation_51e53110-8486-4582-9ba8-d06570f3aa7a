"""
Template tags for AI Assistant
"""

from django import template
from django.utils.safestring import mark_safe
from django.template.loader import render_to_string

register = template.Library()


@register.inclusion_tag('ai_assistant/floating_widget.html', takes_context=True)
def ai_floating_widget(context, **kwargs):
    """
    Include the floating AI assistant widget
    
    Usage:
    {% load ai_assistant_tags %}
    {% ai_floating_widget %}
    
    Or with custom options:
    {% ai_floating_widget enable_voice=True enable_files=True position="bottom-left" %}
    """
    default_config = {
        'enable_voice': True,
        'enable_file_upload': True,
        'max_file_size': 10 * 1024 * 1024,  # 10MB
        'position': 'bottom-right',
        'theme': 'light',
        'allowed_file_types': [
            'image/*', 
            'application/pdf', 
            '.doc', 
            '.docx', 
            '.txt', 
            '.csv'
        ]
    }
    
    # Update with any provided kwargs
    default_config.update(kwargs)
    
    return {
        'config': default_config,
        'request': context.get('request'),
        'user': context.get('user'),
    }


@register.simple_tag
def ai_widget_config(**kwargs):
    """
    Generate JavaScript configuration for the AI widget
    
    Usage:
    {% ai_widget_config enable_voice=True max_file_size=5242880 %}
    """
    import json
    
    default_config = {
        'enableVoice': True,
        'enableFileUpload': True,
        'maxFileSize': 10 * 1024 * 1024,  # 10MB
        'position': 'bottom-right',
        'theme': 'light',
        'allowedFileTypes': [
            'image/*', 
            'application/pdf', 
            '.doc', 
            '.docx', 
            '.txt', 
            '.csv'
        ]
    }
    
    # Convert snake_case to camelCase for JavaScript
    js_config = {}
    for key, value in kwargs.items():
        # Convert snake_case to camelCase
        camel_key = ''.join(word.capitalize() if i > 0 else word for i, word in enumerate(key.split('_')))
        js_config[camel_key] = value
    
    # Merge with defaults
    default_config.update(js_config)
    
    return mark_safe(json.dumps(default_config))


@register.simple_tag(takes_context=True)
def ai_widget_urls(context):
    """
    Generate API URLs for the AI widget
    
    Usage:
    {% ai_widget_urls %}
    """
    from django.urls import reverse
    
    try:
        urls = {
            'messageUrl': reverse('ai_assistant:chat_message'),
            'uploadUrl': reverse('ai_assistant:file_upload'),
            'historyUrl': reverse('ai_assistant:chat_history'),
        }
    except:
        # Fallback URLs if reverse fails
        urls = {
            'messageUrl': '/ai-assistant/api/message/',
            'uploadUrl': '/ai-assistant/api/upload/',
            'historyUrl': '/ai-assistant/api/history/',
        }
    
    import json
    return mark_safe(json.dumps(urls))


@register.filter
def file_size_format(bytes_size):
    """
    Format file size in human readable format
    
    Usage:
    {{ file.size|file_size_format }}
    """
    try:
        bytes_size = int(bytes_size)
    except (ValueError, TypeError):
        return "0 B"
    
    if bytes_size == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    import math
    i = int(math.floor(math.log(bytes_size, 1024)))
    p = math.pow(1024, i)
    s = round(bytes_size / p, 2)
    
    return f"{s} {size_names[i]}"


@register.inclusion_tag('ai_assistant/widget_status.html', takes_context=True)
def ai_widget_status(context):
    """
    Show AI widget status indicator
    
    Usage:
    {% ai_widget_status %}
    """
    from ai_assistant.models import AIAssistantSettings
    
    try:
        settings = AIAssistantSettings.objects.filter(is_active=True).first()
        status = {
            'rasa_enabled': settings.enable_rasa if settings else False,
            'langchain_enabled': settings.enable_langchain if settings else False,
            'memory_enabled': settings.enable_memory if settings else False,
            'is_online': True,  # Could check actual service status
        }
    except:
        status = {
            'rasa_enabled': False,
            'langchain_enabled': False,
            'memory_enabled': False,
            'is_online': False,
        }
    
    return {
        'status': status,
        'user': context.get('user'),
    }


@register.simple_tag
def ai_quick_actions():
    """
    Generate quick action buttons for the AI widget
    
    Usage:
    {% ai_quick_actions %}
    """
    actions = [
        {
            'text': 'Leave Balance',
            'message': 'Check my leave balance',
            'icon': '🏖️'
        },
        {
            'text': 'Attendance',
            'message': 'View my attendance',
            'icon': '📊'
        },
        {
            'text': 'Apply Leave',
            'message': 'Apply for leave',
            'icon': '📝'
        },
        {
            'text': 'Employee Search',
            'message': 'Find employee profiles',
            'icon': '👥'
        },
        {
            'text': 'Payroll',
            'message': 'Help with payroll',
            'icon': '💰'
        },
        {
            'text': 'Policies',
            'message': 'Show company policies',
            'icon': '📋'
        }
    ]
    
    import json
    return mark_safe(json.dumps(actions))


@register.simple_tag
def ai_widget_version():
    """
    Get AI widget version
    
    Usage:
    {% ai_widget_version %}
    """
    return "1.0.0"
