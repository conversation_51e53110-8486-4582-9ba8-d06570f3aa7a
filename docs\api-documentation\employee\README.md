# Employee Module API Documentation

## 📋 Overview

The Employee module provides comprehensive APIs for managing employee operations in the Eaglora HRMS system.

## 🔗 Base URL
```
/api/employee/
```

## 📑 Available Endpoints

| Endpoint | Method | Description | File |
|----------|--------|-------------|------|
| `/employees/` | GET | List all employees with filtering and pagination | [employees-list.md](employees-list.md) |
| `/employees/` | POST | Create new employee record | [employee-create.md](employee-create.md) |
| `/employees/{id}/` | GET | Get detailed employee information | [employee-detail.md](employee-detail.md) |
| `/employees/{id}/` | PUT | Update employee information | [employee-update.md](employee-update.md) |
| `/employees/{id}/` | DELETE | Delete employee record | [employee-delete.md](employee-delete.md) |
| `/employee-type/` | GET | List all employee types | [employee-types-list.md](employee-types-list.md) |
| `/employee-type/` | POST | Create new employee type | [employee-types-create.md](employee-types-create.md) |
| `/employee-type/{id}` | GET/PUT | Get or update employee type details | [employee-types-detail.md](employee-types-detail.md) |
| `/list/employees/` | GET | Advanced employee search with pagination | [employee-search-advanced.md](employee-search-advanced.md) |
| `/employee-selector/` | GET | Hierarchy-based employee selection | [employee-selector.md](employee-selector.md) |
| `/employee-work-information/` | GET | List employee work information | [employee-work-info-list.md](employee-work-info-list.md) |
| `/employee-work-information/` | POST | Create employee work information | [employee-work-info-create.md](employee-work-info-create.md) |
| `/employee-work-information/{id}/` | GET/PUT | Get or update employee work information | [employee-work-info-detail.md](employee-work-info-detail.md) |
| `/employee-bank-details/{id}/` | GET/PUT | Manage employee banking information | [employee-bank-details.md](employee-bank-details.md) |
| `/employee-bulk-update/` | PUT | Bulk update employee records | [employee-bulk-update.md](employee-bulk-update.md) |
| `/employee-work-info-export/` | GET | Export employee work information | [employee-work-info-export.md](employee-work-info-export.md) |
| `/employee-work-info-import/` | POST | Import employee work information | [employee-work-info-import.md](employee-work-info-import.md) |
| `/disciplinary-action/` | GET | List disciplinary actions | [disciplinary-action-list.md](disciplinary-action-list.md) |
| `/disciplinary-action/` | POST | Create disciplinary action | [disciplinary-action-create.md](disciplinary-action-create.md) |
| `/disciplinary-action/{id}/` | GET/PUT | Get or update disciplinary action | [disciplinary-action-detail.md](disciplinary-action-detail.md) |
| `/disciplinary-action-type/` | GET | List disciplinary action types | [disciplinary-action-type-list.md](disciplinary-action-type-list.md) |
| `/disciplinary-action-type/{id}/` | GET/PUT | Get or update disciplinary action type | [disciplinary-action-type-detail.md](disciplinary-action-type-detail.md) |
| `/policies/` | GET | List company policies | [policies-list.md](policies-list.md) |
| `/policies/` | POST | Create company policy | [policies-create.md](policies-create.md) |
| `/policies/{id}/` | GET/PUT | Get or update company policy | [policies-detail.md](policies-detail.md) |
| `/document-request/` | GET | List document requests | [document-request-list.md](document-request-list.md) |
| `/document-request/` | POST | Create document request | [document-request-create.md](document-request-create.md) |
| `/document-request/{id}/` | GET/PUT | Get or update document request | [document-request-detail.md](document-request-detail.md) |
| `/document-bulk-approve-reject/` | POST | Bulk approve or reject document requests | [document-bulk-approve-reject.md](document-bulk-approve-reject.md) |
| `/document-request-approve-reject/{id}/{status}/` | POST | Approve or reject document request | [document-request-approve-reject.md](document-request-approve-reject.md) |
| `/documents/` | GET | List employee documents | [documents-list.md](documents-list.md) |
| `/documents/` | POST | Create employee document | [documents-create.md](documents-create.md) |
| `/documents/{id}/` | GET/PUT | Get or update employee document | [documents-detail.md](documents-detail.md) |
| `/employee-bulk-archive/{status}/` | POST | Bulk archive or unarchive employees | [employee-bulk-archive.md](employee-bulk-archive.md) |
| `/employee-archive/{id}/{status}/` | POST | Archive or unarchive employee | [employee-archive.md](employee-archive.md) |
| `/manager-check/` | GET | Check manager relationships and permissions | [manager-check.md](manager-check.md) |

## 🔐 Authentication & Permissions

All endpoints require JWT authentication:

```http
Authorization: Bearer YOUR_JWT_TOKEN
```

## 🚀 Quick Start

### Basic Request Example
```bash
curl -X GET "/api/employee/" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

## 📊 Use Cases

This module is commonly used for:
- **Access control**
- **Action categories**
- **Action management**
- **Admin operations**
- **Admin panels**
- **Advanced search**
- **Approval workflows**
- **Backup**
- **Banking details**
- **Bulk import**
- **Bulk modifications**
- **Bulk operations**
- **Bulk processing**
- **Bulk upload**
- **Compliance**
- **Configuration**
- **Data cleanup**
- **Data export**
- **Data import**
- **Data management**
- **Data migration**
- **Document access**
- **Document management**
- **Document requests**
- **Document upload**
- **Documentation**
- **Employee categorization**
- **Employee classification**
- **Employee details**
- **Employee directory**
- **Employee files**
- **Employee handbook**
- **Employee management**
- **Employee onboarding**
- **Employee records**
- **Employee requests**
- **Employee self-service**
- **Employee status**
- **Employee termination**
- **File management**
- **File updates**
- **Filtered results**
- **HR actions**
- **HR approval**
- **HR categories**
- **HR configuration**
- **HR documentation**
- **HR efficiency**
- **HR management**
- **HR modifications**
- **HR onboarding**
- **HR operations**
- **HR processing**
- **HR records**
- **HR setup**
- **HR workflow**
- **Hierarchy check**
- **Job assignment**
- **Job details**
- **Job updates**
- **Manager selection**
- **Mass updates**
- **Migration**
- **Mobile apps**
- **Organizational structure**
- **Payment processing**
- **Payroll setup**
- **Permission validation**
- **Policy creation**
- **Policy management**
- **Policy updates**
- **Processing**
- **Profile pages**
- **Profile updates**
- **Record management**
- **Reporting**
- **Reporting hierarchy**
- **Reporting structure**
- **Request management**
- **Request processing**
- **Role changes**
- **Search interface**
- **Self-service**
- **Status updates**
- **Type management**
- **Updates**
- **Workflow**
- **Workflow management**

## 🔄 Related Modules

- [Employee Module](../employee/README.md)
- [Attendance Module](../attendance/README.md)
- [Leave Module](../leave/README.md)
- [Payroll Module](../payroll/README.md)

---

**Navigate to specific endpoints using the links above.**
