# Floating AI Assistant Widget Guide

## Overview

The Floating AI Assistant Widget is a modern, interactive chatbot interface that provides text, voice, and file input capabilities. It floats on the screen as a sleek button and expands into a full-featured chat interface when clicked.

## Features

### 🎨 **Modern UI Design**
- Floating button with gradient background
- Smooth animations and transitions
- Mobile-responsive design
- Dark mode support
- Accessibility features

### 💬 **Text Input**
- Auto-resizing textarea
- Enter key to send messages
- Typing indicators
- Message history
- Quick action buttons

### 🎤 **Voice Input**
- Speech recognition support
- Voice recording with visual feedback
- Audio file upload
- Real-time transcription

### 📎 **File Upload**
- Drag & drop support
- Multiple file types (PDF, DOC, images, etc.)
- File size validation
- Preview functionality
- Content extraction

### 🧭 **Smart Navigation**
- Automatic page redirection
- Employee profile navigation
- HR module integration
- Context-aware responses

## Installation

### 1. Include in Base Template
```html
<!-- In your base.html template -->
{% load ai_assistant_tags %}
{% ai_floating_widget %}
```

### 2. Manual Integration
```html
<!-- Include CSS and JS -->
<link rel="stylesheet" href="{% static 'ai_assistant/css/floating-widget.css' %}">
<script src="{% static 'ai_assistant/js/floating-widget.js' %}"></script>

<!-- Initialize widget -->
<script>
const aiWidget = new FloatingAIWidget({
    apiUrl: '/ai-assistant/api/message/',
    uploadUrl: '/ai-assistant/api/upload/',
    enableVoice: true,
    enableFileUpload: true
});
</script>
```

### 3. Django URLs
Add to your main `urls.py`:
```python
path('ai-assistant/', include('ai_assistant.urls')),
```

## Configuration Options

### Basic Configuration
```javascript
const aiWidget = new FloatingAIWidget({
    // API endpoints
    apiUrl: '/ai-assistant/api/message/',
    uploadUrl: '/ai-assistant/api/upload/',
    
    // Features
    enableVoice: true,
    enableFileUpload: true,
    
    // File upload settings
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedFileTypes: ['image/*', 'application/pdf', '.doc', '.docx'],
    
    // UI settings
    position: 'bottom-right', // or 'bottom-left'
    theme: 'light' // or 'dark'
});
```

### Template Tag Configuration
```html
{% load ai_assistant_tags %}

<!-- Basic usage -->
{% ai_floating_widget %}

<!-- With custom options -->
{% ai_floating_widget enable_voice=True enable_files=True position="bottom-left" %}

<!-- Get configuration as JSON -->
{% ai_widget_config enable_voice=True max_file_size=5242880 %}
```

## Supported File Types

### Documents
- PDF files (`.pdf`)
- Word documents (`.doc`, `.docx`)
- Text files (`.txt`)
- CSV files (`.csv`)
- Excel files (`.xlsx`)
- PowerPoint (`.pptx`)

### Images
- JPEG (`.jpg`, `.jpeg`)
- PNG (`.png`)
- GIF (`.gif`)
- WebP (`.webp`)

### Audio
- WAV (`.wav`)
- MP3 (`.mp3`)
- Voice recordings

## Voice Features

### Speech Recognition
- Uses Web Speech API
- Supports multiple languages
- Real-time transcription
- Automatic message sending

### Voice Recording
- MediaRecorder API
- Visual recording indicator
- Audio file upload
- Fallback for unsupported browsers

### Browser Support
- Chrome/Edge: Full support
- Firefox: Limited support
- Safari: Basic support
- Mobile: Varies by device

## API Endpoints

### Message API
```
POST /ai-assistant/api/message/
Content-Type: application/json

{
    "message": "User message text",
    "session_id": "unique_session_id"
}
```

### File Upload API
```
POST /ai-assistant/api/upload/
Content-Type: multipart/form-data

file: [File object]
type: "file" | "voice"
```

## Customization

### CSS Variables
```css
:root {
    --ai-primary-color: #667eea;
    --ai-secondary-color: #764ba2;
    --ai-success-color: #2ed573;
    --ai-error-color: #ff4757;
}
```

### Custom Themes
```css
/* Dark theme */
[data-theme="dark"] {
    --ai-bg-color: #2c2c2c;
    --ai-text-color: #fff;
    --ai-border-color: #555;
}
```

### Position Override
```css
.ai-widget-container.bottom-left {
    bottom: 20px;
    left: 20px;
    right: auto;
}
```

## Quick Actions

### Default Actions
- Check Leave Balance
- View Attendance
- Apply for Leave
- Employee Search
- Payroll Help
- Company Policies

### Custom Actions
```javascript
const customActions = [
    {
        text: 'Custom Action',
        message: 'Execute custom command',
        icon: '⚡'
    }
];

aiWidget.setQuickActions(customActions);
```

## Events and Callbacks

### Widget Events
```javascript
// Widget opened
aiWidget.on('open', () => {
    console.log('Widget opened');
});

// Message sent
aiWidget.on('message', (data) => {
    console.log('Message sent:', data);
});

// File uploaded
aiWidget.on('upload', (file) => {
    console.log('File uploaded:', file);
});
```

## Mobile Optimization

### Responsive Design
- Full-screen on mobile devices
- Touch-friendly interface
- Optimized for small screens
- Gesture support

### Performance
- Lazy loading
- Efficient DOM manipulation
- Minimal memory footprint
- Battery-conscious animations

## Accessibility

### ARIA Support
- Screen reader compatible
- Keyboard navigation
- Focus management
- Semantic HTML

### Keyboard Shortcuts
- `Enter`: Send message
- `Shift + Enter`: New line
- `Esc`: Close widget
- `Tab`: Navigate elements

## Browser Compatibility

### Supported Browsers
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- Mobile browsers

### Fallbacks
- No voice support: Text input only
- No file API: Basic upload
- Old browsers: Graceful degradation

## Troubleshooting

### Common Issues

**Widget not appearing:**
- Check CSS/JS file paths
- Verify Django URLs
- Check browser console for errors

**Voice not working:**
- Enable microphone permissions
- Use HTTPS (required for voice)
- Check browser compatibility

**File upload failing:**
- Check file size limits
- Verify allowed file types
- Check server permissions

### Debug Mode
```javascript
const aiWidget = new FloatingAIWidget({
    debug: true, // Enable console logging
    // ... other options
});
```

## Demo

Visit the demo page to see the widget in action:
```
/ai-assistant/demo/widget/
```

## Performance Tips

1. **Lazy Load**: Include widget only on pages where needed
2. **File Limits**: Set appropriate file size limits
3. **Caching**: Enable browser caching for static assets
4. **CDN**: Use CDN for better performance
5. **Compression**: Enable gzip compression

## Security Considerations

1. **File Validation**: Always validate uploaded files
2. **CSRF Protection**: Include CSRF tokens
3. **Rate Limiting**: Implement API rate limiting
4. **Input Sanitization**: Sanitize user inputs
5. **File Scanning**: Scan uploaded files for malware

## Future Enhancements

- [ ] Video call support
- [ ] Screen sharing
- [ ] Multi-language support
- [ ] Advanced file processing
- [ ] Integration with more HR modules
- [ ] Analytics and reporting
- [ ] Custom branding options
