# Base Module API Documentation

## 📋 Overview

The Base module provides comprehensive APIs for managing base operations in the Eaglora HRMS system.

## 🔗 Base URL
```
/api/base/
```

## 📑 Available Endpoints

| Endpoint | Method | Description | File |
|----------|--------|-------------|------|
| `/company/` | GET | List companies | [company-list.md](company-list.md) |
| `/company/` | POST | Create company | [company-create.md](company-create.md) |
| `/company/{id}/` | GET/PUT | Get or update company details | [company-detail.md](company-detail.md) |
| `/department/` | GET | List departments | [department-list.md](department-list.md) |
| `/department/` | POST | Create department | [department-create.md](department-create.md) |
| `/department/{id}/` | GET/PUT | Get or update department details | [department-detail.md](department-detail.md) |
| `/job-position/` | GET | List job positions | [job-position-list.md](job-position-list.md) |
| `/job-position/` | POST | Create job position | [job-position-create.md](job-position-create.md) |
| `/job-position/{id}/` | GET/PUT | Get or update job position details | [job-position-detail.md](job-position-detail.md) |
| `/job-role/` | GET | List job roles | [job-role-list.md](job-role-list.md) |
| `/job-role/` | POST | Create job role | [job-role-create.md](job-role-create.md) |
| `/job-role/{id}/` | GET/PUT | Get or update job role details | [job-role-detail.md](job-role-detail.md) |
| `/work-type/` | GET | List work types | [work-type-list.md](work-type-list.md) |
| `/work-type/` | POST | Create work type | [work-type-create.md](work-type-create.md) |
| `/work-type/{id}/` | GET/PUT | Get or update work type details | [work-type-detail.md](work-type-detail.md) |
| `/shift/` | GET | List work shifts | [shift-list.md](shift-list.md) |
| `/shift/` | POST | Create work shift | [shift-create.md](shift-create.md) |
| `/shift/{id}/` | GET/PUT | Get or update shift details | [shift-detail.md](shift-detail.md) |
| `/employee-shift/` | GET | List employee shift assignments | [employee-shift-list.md](employee-shift-list.md) |
| `/employee-shift/` | POST | Create employee shift assignment | [employee-shift-create.md](employee-shift-create.md) |
| `/employee-shift/{id}/` | GET/PUT | Get or update employee shift assignment | [employee-shift-detail.md](employee-shift-detail.md) |
| `/employee-shift-schedule/` | GET | List employee shift schedules | [employee-shift-schedule-list.md](employee-shift-schedule-list.md) |
| `/employee-shift-schedule/` | POST | Create employee shift schedule | [employee-shift-schedule-create.md](employee-shift-schedule-create.md) |
| `/employee-shift-schedule/{id}/` | GET/PUT | Get or update employee shift schedule | [employee-shift-schedule-detail.md](employee-shift-schedule-detail.md) |
| `/rotating-shift-assign/` | GET | List rotating shift assignments | [rotating-shift-assign-list.md](rotating-shift-assign-list.md) |
| `/rotating-shift-assign/` | POST | Create rotating shift assignment | [rotating-shift-assign-create.md](rotating-shift-assign-create.md) |
| `/rotating-shift-assign/{id}/` | GET/PUT | Get or update rotating shift assignment | [rotating-shift-assign-detail.md](rotating-shift-assign-detail.md) |
| `/rotating-work-type-assign/` | GET | List rotating work type assignments | [rotating-work-type-assign-list.md](rotating-work-type-assign-list.md) |
| `/rotating-work-type-assign/` | POST | Create rotating work type assignment | [rotating-work-type-assign-create.md](rotating-work-type-assign-create.md) |
| `/rotating-work-type-assign/{id}/` | GET/PUT | Get or update rotating work type assignment | [rotating-work-type-assign-detail.md](rotating-work-type-assign-detail.md) |

## 🔐 Authentication & Permissions

All endpoints require JWT authentication:

```http
Authorization: Bearer YOUR_JWT_TOKEN
```

## 🚀 Quick Start

### Basic Request Example
```bash
curl -X GET "/api/base/" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

## 📊 Use Cases

This module is commonly used for:
- **Access control**
- **Access control changes**
- **Arrangement changes**
- **Assignment changes**
- **Assignment management**
- **Calendar changes**
- **Career progression**
- **Classification updates**
- **Company management**
- **Company setup**
- **Configuration**
- **Department creation**
- **Department management**
- **Dynamic assignments**
- **Dynamic scheduling**
- **Dynamic work arrangements**
- **Employee schedules**
- **Employment classification**
- **Employment types**
- **Flexible arrangements**
- **Flexible scheduling**
- **Job structure**
- **Job structure changes**
- **Multi-tenant configuration**
- **Multi-tenant support**
- **Organization changes**
- **Organization creation**
- **Organization management**
- **Organization setup**
- **Organization updates**
- **Organizational structure**
- **Permission setup**
- **Permission structure**
- **Permission updates**
- **Position creation**
- **Position management**
- **Reporting hierarchy**
- **Role creation**
- **Role definition**
- **Role management**
- **Role updates**
- **Rotating assignments**
- **Rotating shifts**
- **Rotation management**
- **Schedule changes**
- **Schedule creation**
- **Schedule management**
- **Schedule planning**
- **Schedule updates**
- **Shift assignment**
- **Shift assignments**
- **Shift changes**
- **Shift management**
- **Shift planning**
- **Shift rotation**
- **Shift rotation setup**
- **Shift setup**
- **Shift updates**
- **Structure building**
- **Structure updates**
- **Time adjustments**
- **Time tracking**
- **Work allocation**
- **Work arrangements**
- **Work calendar**
- **Work calendar setup**
- **Work classification**
- **Work planning**
- **Work schedules**
- **Work time management**
- **Work type management**
- **Work type rotation**
- **Work type rotation setup**
- **Work type setup**
- **Work type updates**

## 🔄 Related Modules

- [Employee Module](../employee/README.md)
- [Attendance Module](../attendance/README.md)
- [Leave Module](../leave/README.md)
- [Payroll Module](../payroll/README.md)

---

**Navigate to specific endpoints using the links above.**
