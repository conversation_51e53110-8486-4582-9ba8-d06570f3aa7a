<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Widget Test</title>
    {% load static %}
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        .test-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.success {
            background: rgba(46, 213, 115, 0.3);
        }
        .status.error {
            background: rgba(255, 71, 87, 0.3);
        }
        .widget-indicator {
            position: fixed;
            bottom: 100px;
            right: 30px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 14px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            animation: bounce 2s infinite;
        }
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🤖 AI Assistant Widget Test</h1>
        <p>This page tests if the floating AI assistant widget loads correctly.</p>
        
        <div class="test-info">
            <h3>Test Results:</h3>
            <div id="css-status" class="status">⏳ Loading CSS...</div>
            <div id="js-status" class="status">⏳ Loading JavaScript...</div>
            <div id="widget-status" class="status">⏳ Initializing Widget...</div>
        </div>
        
        <div class="test-info">
            <h3>Instructions:</h3>
            <p>1. Look for the floating button in the bottom-right corner</p>
            <p>2. Click the button to open the chat widget</p>
            <p>3. Try typing a message like "get admin profile"</p>
            <p>4. Test voice input (if supported by your browser)</p>
            <p>5. Test file upload functionality</p>
        </div>
        
        <div class="test-info">
            <h3>Debug Information:</h3>
            <div id="debug-info">
                <p>CSS URL: {% static 'ai_assistant/css/floating-widget.css' %}</p>
                <p>JS URL: {% static 'ai_assistant/js/floating-widget.js' %}</p>
                <p>API URL: /ai-assistant/api/message/</p>
                <p>Upload URL: /ai-assistant/api/upload/</p>
            </div>
        </div>
    </div>
    
    <!-- Widget Indicator -->
    <div class="widget-indicator" id="widgetIndicator">
        Look for the AI button! →
    </div>
    
    <!-- Load the floating widget CSS -->
    <link rel="stylesheet" href="{% static 'ai_assistant/css/floating-widget.css' %}" onload="updateStatus('css-status', 'success', '✅ CSS Loaded')" onerror="updateStatus('css-status', 'error', '❌ CSS Failed to Load')">
    
    <!-- Load the floating widget JavaScript -->
    <script src="{% static 'ai_assistant/js/floating-widget.js' %}" onload="updateStatus('js-status', 'success', '✅ JavaScript Loaded')" onerror="updateStatus('js-status', 'error', '❌ JavaScript Failed to Load')"></script>
    
    <!-- Initialize the widget -->
    <script>
        function updateStatus(elementId, type, message) {
            const element = document.getElementById(elementId);
            element.className = `status ${type}`;
            element.textContent = message;
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing widget...');
            
            // Check if FloatingAIWidget class is available
            if (typeof FloatingAIWidget !== 'undefined') {
                try {
                    window.aiWidget = new FloatingAIWidget({
                        apiUrl: '/ai-assistant/api/message/',
                        uploadUrl: '/ai-assistant/api/upload/',
                        enableVoice: true,
                        enableFileUpload: true,
                        maxFileSize: 10 * 1024 * 1024,
                        allowedFileTypes: ['image/*', 'application/pdf', '.doc', '.docx', '.txt', '.csv'],
                        theme: 'light',
                        position: 'bottom-right'
                    });
                    
                    updateStatus('widget-status', 'success', '✅ Widget Initialized Successfully');
                    console.log('AI Widget initialized successfully');
                    
                    // Hide indicator after widget is opened
                    const indicator = document.getElementById('widgetIndicator');
                    const checkWidget = setInterval(() => {
                        if (window.aiWidget && window.aiWidget.isOpen) {
                            indicator.style.display = 'none';
                            clearInterval(checkWidget);
                        }
                    }, 1000);
                    
                    // Auto-hide indicator after 10 seconds
                    setTimeout(() => {
                        indicator.style.opacity = '0';
                        setTimeout(() => {
                            indicator.style.display = 'none';
                        }, 500);
                    }, 10000);
                    
                } catch (error) {
                    updateStatus('widget-status', 'error', '❌ Widget Initialization Failed: ' + error.message);
                    console.error('Widget initialization error:', error);
                }
            } else {
                updateStatus('widget-status', 'error', '❌ FloatingAIWidget Class Not Found');
                console.error('FloatingAIWidget class not found');
            }
        });
        
        // Additional debugging
        window.addEventListener('load', function() {
            console.log('Page fully loaded');
            console.log('FloatingAIWidget available:', typeof FloatingAIWidget !== 'undefined');
        });
    </script>
    
    {% csrf_token %}
</body>
</html>
