#!/usr/bin/env python3
"""
Eaglora HRMS AI Model Trainer

This script trains an AI model using the comprehensive training data generated
from the API documentation. It creates a natural language to API mapping model.
"""

import json
import os
import numpy as np
import pandas as pd
from datetime import datetime
from typing import List, Dict, Any, Tuple
import pickle
import re
from collections import defaultdict

# ML/AI Libraries
try:
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.model_selection import train_test_split
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.metrics import accuracy_score, classification_report
    from sklearn.preprocessing import LabelEncoder
    import joblib
except ImportError:
    print("Installing required ML libraries...")
    os.system("pip install scikit-learn pandas numpy")
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.model_selection import train_test_split
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.metrics import accuracy_score, classification_report
    from sklearn.preprocessing import LabelEncoder
    import joblib

class EagloraAIModelTrainer:
    def __init__(self, training_data_dir: str = "ai_training/data"):
        self.training_data_dir = training_data_dir
        self.models = {}
        self.vectorizers = {}
        self.label_encoders = {}
        self.api_mappings = {}
        
        # Load training data
        self.training_data = self.load_training_data()
        
        # Prepare training datasets
        self.prepare_training_datasets()
    
    def load_training_data(self) -> Dict[str, Any]:
        """Load all training data from files"""
        print("📂 Loading training data...")
        
        complete_file = os.path.join(self.training_data_dir, "complete_training_data.json")
        
        if not os.path.exists(complete_file):
            raise FileNotFoundError(f"Training data not found at {complete_file}")
        
        with open(complete_file, 'r') as f:
            training_data = json.load(f)
        
        print(f"✅ Loaded training data for {len(training_data)} modules")
        return training_data
    
    def prepare_training_datasets(self):
        """Prepare training datasets for different AI tasks"""
        print("🔄 Preparing training datasets...")
        
        # Dataset for intent classification (which API to call)
        self.intent_data = []
        
        # Dataset for parameter extraction
        self.parameter_data = []
        
        # Dataset for search query enhancement
        self.search_data = []
        
        # API endpoint mappings
        self.api_mappings = {}
        
        for module_name, module_data in self.training_data.items():
            for endpoint_data in module_data:
                endpoint_name = endpoint_data["endpoint_name"]
                
                for example in endpoint_data["examples"]:
                    input_text = example["input"]
                    output_data = example["output"]
                    
                    # Intent classification data
                    intent_label = f"{module_name}_{endpoint_name}"
                    self.intent_data.append({
                        "text": input_text,
                        "intent": intent_label,
                        "module": module_name,
                        "endpoint": endpoint_name,
                        "api_endpoint": output_data["api_endpoint"],
                        "method": output_data["method"],
                        "confidence": output_data["confidence"]
                    })
                    
                    # Search data (for endpoints that support search)
                    if "search_term" in output_data:
                        self.search_data.append({
                            "text": input_text,
                            "search_term": output_data["search_term"],
                            "api_endpoint": output_data["api_endpoint"],
                            "base_endpoint": output_data["api_endpoint"].split("?")[0]
                        })
                    
                    # Store API mappings
                    if intent_label not in self.api_mappings:
                        self.api_mappings[intent_label] = {
                            "api_endpoint": output_data["api_endpoint"].split("?")[0],  # Base URL without params
                            "method": output_data["method"],
                            "module": module_name,
                            "endpoint": endpoint_name,
                            "entity": output_data["entity"],
                            "action": output_data["action"],
                            "supports_search": "search_term" in output_data
                        }
        
        print(f"✅ Prepared {len(self.intent_data):,} intent examples")
        print(f"✅ Prepared {len(self.search_data):,} search examples")
    
    def train_intent_classifier(self) -> Dict[str, Any]:
        """Train the intent classification model"""
        print("\n🧠 Training Intent Classification Model...")
        
        # Prepare data
        texts = [item["text"] for item in self.intent_data]
        intents = [item["intent"] for item in self.intent_data]
        
        # Create TF-IDF vectorizer
        vectorizer = TfidfVectorizer(
            max_features=10000,
            ngram_range=(1, 3),
            stop_words='english',
            lowercase=True
        )
        
        # Vectorize texts
        X = vectorizer.fit_transform(texts)
        
        # Encode labels
        label_encoder = LabelEncoder()
        y = label_encoder.fit_transform(intents)
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # Train model
        model = RandomForestClassifier(
            n_estimators=200,
            max_depth=20,
            random_state=42,
            n_jobs=-1
        )
        
        print("  🔄 Training Random Forest classifier...")
        model.fit(X_train, y_train)
        
        # Evaluate model
        y_pred = model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        
        print(f"  ✅ Intent Classification Accuracy: {accuracy:.4f}")
        
        # Store models
        self.models["intent_classifier"] = model
        self.vectorizers["intent"] = vectorizer
        self.label_encoders["intent"] = label_encoder
        
        return {
            "accuracy": accuracy,
            "model": model,
            "vectorizer": vectorizer,
            "label_encoder": label_encoder
        }
    
    def train_search_extractor(self) -> Dict[str, Any]:
        """Train the search term extraction model"""
        print("\n🔍 Training Search Term Extraction Model...")
        
        if not self.search_data:
            print("  ⚠️  No search data available, skipping search model training")
            return {}
        
        # Prepare data for search term extraction
        search_texts = [item["text"] for item in self.search_data]
        search_terms = [item["search_term"] for item in self.search_data]
        
        # Create vectorizer for search
        search_vectorizer = TfidfVectorizer(
            max_features=5000,
            ngram_range=(1, 2),
            stop_words='english',
            lowercase=True
        )
        
        X_search = search_vectorizer.fit_transform(search_texts)
        
        # Encode search terms
        search_encoder = LabelEncoder()
        y_search = search_encoder.fit_transform(search_terms)
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X_search, y_search, test_size=0.2, random_state=42
        )
        
        # Train search model
        search_model = RandomForestClassifier(
            n_estimators=100,
            max_depth=15,
            random_state=42,
            n_jobs=-1
        )
        
        print("  🔄 Training search term extractor...")
        search_model.fit(X_train, y_train)
        
        # Evaluate
        y_pred = search_model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        
        print(f"  ✅ Search Extraction Accuracy: {accuracy:.4f}")
        
        # Store models
        self.models["search_extractor"] = search_model
        self.vectorizers["search"] = search_vectorizer
        self.label_encoders["search"] = search_encoder
        
        return {
            "accuracy": accuracy,
            "model": search_model,
            "vectorizer": search_vectorizer,
            "label_encoder": search_encoder
        }
    
    def create_api_predictor(self) -> Dict[str, Any]:
        """Create the main API prediction system"""
        print("\n🎯 Creating API Prediction System...")
        
        class EagloraAPIPredictor:
            def __init__(self, models, vectorizers, label_encoders, api_mappings):
                self.models = models
                self.vectorizers = vectorizers
                self.label_encoders = label_encoders
                self.api_mappings = api_mappings
            
            def predict_api_call(self, user_input: str) -> Dict[str, Any]:
                """Predict the API call for a given user input"""
                
                # Clean and preprocess input
                cleaned_input = self.preprocess_input(user_input)
                
                # Predict intent
                intent_vector = self.vectorizers["intent"].transform([cleaned_input])
                intent_pred = self.models["intent_classifier"].predict(intent_vector)[0]
                intent_proba = self.models["intent_classifier"].predict_proba(intent_vector)[0]
                intent_confidence = max(intent_proba)
                
                # Decode intent
                intent_label = self.label_encoders["intent"].inverse_transform([intent_pred])[0]
                
                # Get API mapping
                api_info = self.api_mappings.get(intent_label, {})
                
                # Extract search terms if applicable
                search_term = None
                if api_info.get("supports_search", False) and "search_extractor" in self.models:
                    search_term = self.extract_search_term(cleaned_input)
                
                # Build API call
                api_endpoint = api_info.get("api_endpoint", "")
                if search_term and api_info.get("supports_search", False):
                    api_endpoint += f"?search={search_term}"
                
                return {
                    "intent": intent_label,
                    "confidence": float(intent_confidence),
                    "api_endpoint": api_endpoint,
                    "method": api_info.get("method", "GET"),
                    "module": api_info.get("module", ""),
                    "action": api_info.get("action", ""),
                    "entity": api_info.get("entity", ""),
                    "search_term": search_term,
                    "raw_input": user_input,
                    "processed_input": cleaned_input
                }
            
            def extract_search_term(self, text: str) -> str:
                """Extract search term from text"""
                if "search_extractor" not in self.models:
                    # Fallback: extract potential search terms using regex
                    return self.extract_search_term_fallback(text)
                
                try:
                    search_vector = self.vectorizers["search"].transform([text])
                    search_pred = self.models["search_extractor"].predict(search_vector)[0]
                    search_term = self.label_encoders["search"].inverse_transform([search_pred])[0]
                    return search_term
                except:
                    return self.extract_search_term_fallback(text)
            
            def extract_search_term_fallback(self, text: str) -> str:
                """Fallback method to extract search terms"""
                # Common patterns for search terms
                patterns = [
                    r'find\s+(\w+)',
                    r'search\s+for\s+(\w+)',
                    r'look\s+for\s+(\w+)',
                    r'show\s+me\s+(\w+)',
                    r'get\s+(\w+)',
                    r'named\s+(\w+)',
                    r'called\s+(\w+)',
                    r'with\s+(\w+)',
                    r'containing\s+(\w+)'
                ]
                
                for pattern in patterns:
                    match = re.search(pattern, text.lower())
                    if match:
                        return match.group(1)
                
                # If no pattern matches, try to find the last word that's not a common word
                words = text.lower().split()
                common_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after', 'above', 'below', 'between', 'among', 'find', 'search', 'show', 'get', 'list', 'display', 'employee', 'staff', 'user', 'person', 'me', 'please', 'can', 'you', 'help', 'want', 'need', 'looking', 'for'}
                
                for word in reversed(words):
                    if word not in common_words and len(word) > 2:
                        return word
                
                return "all"
            
            def preprocess_input(self, text: str) -> str:
                """Preprocess user input"""
                # Convert to lowercase
                text = text.lower()
                
                # Remove extra whitespace
                text = ' '.join(text.split())
                
                # Remove punctuation at the end
                text = text.rstrip('.,!?;:')
                
                return text
        
        # Create predictor instance
        predictor = EagloraAPIPredictor(
            self.models,
            self.vectorizers,
            self.label_encoders,
            self.api_mappings
        )
        
        return {"predictor": predictor}
    
    def train_all_models(self) -> Dict[str, Any]:
        """Train all AI models"""
        print("🚀 Starting comprehensive AI model training...")
        print(f"📊 Training on {len(self.intent_data):,} examples")
        
        results = {}
        
        # Train intent classifier
        intent_results = self.train_intent_classifier()
        results["intent_classifier"] = intent_results
        
        # Train search extractor
        search_results = self.train_search_extractor()
        if search_results:
            results["search_extractor"] = search_results
        
        # Create API predictor
        predictor_results = self.create_api_predictor()
        results["api_predictor"] = predictor_results
        
        print("\n🎉 All models trained successfully!")
        return results
    
    def save_models(self, output_dir: str = "ai_training/models"):
        """Save trained models to disk"""
        print(f"\n💾 Saving trained models to {output_dir}...")
        
        os.makedirs(output_dir, exist_ok=True)
        
        # Save individual models
        for model_name, model in self.models.items():
            model_file = os.path.join(output_dir, f"{model_name}.joblib")
            joblib.dump(model, model_file)
            print(f"  ✅ Saved {model_name}")
        
        # Save vectorizers
        for vec_name, vectorizer in self.vectorizers.items():
            vec_file = os.path.join(output_dir, f"{vec_name}_vectorizer.joblib")
            joblib.dump(vectorizer, vec_file)
            print(f"  ✅ Saved {vec_name} vectorizer")
        
        # Save label encoders
        for enc_name, encoder in self.label_encoders.items():
            enc_file = os.path.join(output_dir, f"{enc_name}_encoder.joblib")
            joblib.dump(encoder, enc_file)
            print(f"  ✅ Saved {enc_name} encoder")
        
        # Save API mappings
        mappings_file = os.path.join(output_dir, "api_mappings.json")
        with open(mappings_file, 'w') as f:
            json.dump(self.api_mappings, f, indent=2)
        print(f"  ✅ Saved API mappings")
        
        # Save complete model package
        model_package = {
            "models": {name: f"{name}.joblib" for name in self.models.keys()},
            "vectorizers": {name: f"{name}_vectorizer.joblib" for name in self.vectorizers.keys()},
            "label_encoders": {name: f"{name}_encoder.joblib" for name in self.label_encoders.keys()},
            "api_mappings": "api_mappings.json",
            "training_date": datetime.now().isoformat(),
            "total_training_examples": len(self.intent_data),
            "supported_modules": list(self.training_data.keys())
        }
        
        package_file = os.path.join(output_dir, "model_package.json")
        with open(package_file, 'w') as f:
            json.dump(model_package, f, indent=2)
        
        print(f"💾 All models saved successfully!")
    
    def test_model_predictions(self, test_queries: List[str] = None):
        """Test the trained model with sample queries"""
        print("\n🧪 Testing model predictions...")
        
        if test_queries is None:
            test_queries = [
                "find employee john",
                "search for staff named jane",
                "show me all employees",
                "clock in now",
                "I want to clock out",
                "create new employee",
                "list all leave requests",
                "approve leave request",
                "show today's attendance",
                "find assets in IT department",
                "get my payslip",
                "show company policies"
            ]
        
        # Create predictor
        predictor_results = self.create_api_predictor()
        predictor = predictor_results["predictor"]
        
        print("\n📋 Test Results:")
        print("-" * 80)
        
        for query in test_queries:
            result = predictor.predict_api_call(query)
            
            print(f"Input: {query}")
            print(f"  → API: {result['method']} {result['api_endpoint']}")
            print(f"  → Module: {result['module']}")
            print(f"  → Action: {result['action']}")
            print(f"  → Confidence: {result['confidence']:.3f}")
            if result['search_term']:
                print(f"  → Search Term: {result['search_term']}")
            print()

def main():
    """Main function to train the AI model"""
    print("🤖 Eaglora HRMS AI Model Trainer")
    print("=" * 50)
    
    # Check if training data exists
    if not os.path.exists("ai_training/data/complete_training_data.json"):
        print("❌ Training data not found!")
        print("Please run ai_training_generator.py first to generate training data.")
        return
    
    # Initialize trainer
    trainer = EagloraAIModelTrainer()
    
    # Train all models
    results = trainer.train_all_models()
    
    # Save models
    trainer.save_models()
    
    # Test predictions
    trainer.test_model_predictions()
    
    print("\n🎯 AI model training completed successfully!")
    print("📁 Models saved to: ai_training/models/")
    print("🚀 Ready to integrate with Eaglora HRMS AI Assistant!")

if __name__ == "__main__":
    main()
