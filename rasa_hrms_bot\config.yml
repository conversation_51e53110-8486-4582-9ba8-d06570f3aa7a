# Configuration for Rasa NLU and Core models
# https://rasa.com/docs/rasa/model-configuration/

recipe: default.v1

# Configuration for Rasa NLU
language: en

pipeline:
  # Tokenization
  - name: WhitespaceTokenizer
  
  # Featurization
  - name: RegexFeaturizer
  - name: LexicalSyntacticFeaturizer
  - name: CountVectorsFeaturizer
  - name: CountVectorsFeaturizer
    analyzer: char_wb
    min_ngram: 1
    max_ngram: 4
  
  # Intent Classification
  - name: DIETClassifier
    epochs: 100
    constrain_similarities: true
    model_confidence: softmax
    
  # Entity Extraction
  - name: EntitySynonymMapper
  - name: ResponseSelector
    epochs: 100
    constrain_similarities: true
    
  # Fallback
  - name: FallbackClassifier
    threshold: 0.3
    ambiguity_threshold: 0.1

# Configuration for Rasa Core
policies:
  - name: MemoizationPolicy
  - name: RulePolicy
    core_fallback_threshold: 0.4
    core_fallback_action_name: "action_default_fallback"
    enable_fallback_prediction: True
  - name: UnexpecTEDIntentPolicy
    max_history: 5
    epochs: 100
  - name: TEDPolicy
    max_history: 5
    epochs: 100
    constrain_similarities: true

# Assistant ID for Rasa X
assistant_id: hrms_assistant
