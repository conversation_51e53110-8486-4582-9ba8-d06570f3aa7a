"""
Views for AI Assistant HRMS Integration
"""

import json
import logging
import time
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any

from django.contrib.auth.decorators import login_required
from django.contrib.auth.models import User
from django.http import JsonResponse
from django.shortcuts import render
from django.utils.decorators import method_decorator
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils import timezone

from .models import AIAssistantSettings, ChatHistory, ConversationMemory
from .rasa_integration import RasaIntegration
from .hrms_api_integration import HRMSAPIIntegration, EmployeeAPIHandler, LeaveAPIHandler, AttendanceAPIHandler

logger = logging.getLogger(__name__)


@method_decorator(login_required, name='dispatch')
class ChatView(View):
    """Main chat interface view"""
    
    def get(self, request):
        """Render chat interface"""
        context = {
            'user': request.user,
            'chat_history': self._get_recent_chat_history(request.user),
            'settings': self._get_ai_settings()
        }
        return render(request, 'ai_assistant/chat.html', context)
    
    def _get_recent_chat_history(self, user: User, limit: int = 10):
        """Get recent chat history for user"""
        return ChatHistory.objects.filter(user=user).order_by('-created_at')[:limit]
    
    def _get_ai_settings(self):
        """Get AI assistant settings"""
        return AIAssistantSettings.objects.filter(is_active=True).first()


@csrf_exempt
@require_http_methods(["POST"])
# @login_required
def chat_message(request):
    """
    Handle chat messages from frontend
    
    Expected JSON payload:
    {
        "message": "user message",
        "session_id": "optional session id"
    }
    """
    try:
        start_time = time.time()
        
        # Parse request data
        data = json.loads(request.body)
        message = data.get('message', '').strip()
        session_id = data.get('session_id', str(uuid.uuid4()))
        
        if not message:
            return JsonResponse({
                'error': 'Message cannot be empty'
            }, status=400)
        
        # Initialize Rasa integration
        rasa_integration = RasaIntegration()
        
        # Send message to Rasa and get response
        response = rasa_integration.send_message(
            message=message,
            sender_id=session_id,
            user=request.user
        )
        
        # Calculate processing time
        processing_time = time.time() - start_time
        response['processing_time'] = round(processing_time, 3)
        response['session_id'] = session_id
        
        return JsonResponse(response)
        
    except json.JSONDecodeError:
        return JsonResponse({
            'error': 'Invalid JSON payload'
        }, status=400)
    except Exception as e:
        logger.error(f"Error in chat_message view: {str(e)}")
        return JsonResponse({
            'error': 'Internal server error'
        }, status=500)


@login_required
def chat_history(request):
    """Get chat history for current user"""
    try:
        page = int(request.GET.get('page', 1))
        limit = int(request.GET.get('limit', 20))
        
        offset = (page - 1) * limit
        
        history = ChatHistory.objects.filter(
            user=request.user
        ).order_by('-created_at')[offset:offset + limit]
        
        history_data = []
        for chat in history:
            history_data.append({
                'id': chat.id,
                'message': chat.message,
                'response': chat.response,
                'intent': chat.intent,
                'confidence': chat.confidence,
                'created_at': chat.created_at.isoformat(),
                'processing_time': chat.processing_time
            })
        
        return JsonResponse({
            'history': history_data,
            'page': page,
            'has_more': len(history) == limit
        })
        
    except Exception as e:
        logger.error(f"Error in chat_history view: {str(e)}")
        return JsonResponse({
            'error': 'Failed to fetch chat history'
        }, status=500)


@csrf_exempt
@require_http_methods(["POST"])
@login_required
def direct_api_call(request):
    """
    Direct API call endpoint for testing HRMS integration
    
    Expected JSON payload:
    {
        "intent": "intent_name",
        "entities": {"entity_name": "entity_value"}
    }
    """
    try:
        data = json.loads(request.body)
        intent = data.get('intent')
        entities = data.get('entities', {})
        
        if not intent:
            return JsonResponse({
                'error': 'Intent is required'
            }, status=400)
        
        # Initialize HRMS API integration
        hrms_api = HRMSAPIIntegration(request.user)
        
        # Execute API request
        response = hrms_api.execute_api_request(intent, entities)
        
        return JsonResponse(response)
        
    except json.JSONDecodeError:
        return JsonResponse({
            'error': 'Invalid JSON payload'
        }, status=400)
    except Exception as e:
        logger.error(f"Error in direct_api_call view: {str(e)}")
        return JsonResponse({
            'error': 'Internal server error'
        }, status=500)


@login_required
def employee_search(request):
    """Search for employees via AI assistant"""
    try:
        search_term = request.GET.get('q', '').strip()
        
        if not search_term:
            return JsonResponse({
                'error': 'Search term is required'
            }, status=400)
        
        # Use employee API handler
        employee_handler = EmployeeAPIHandler(request.user)
        response = employee_handler.search_employees(search_term)
        
        return JsonResponse(response)
        
    except Exception as e:
        logger.error(f"Error in employee_search view: {str(e)}")
        return JsonResponse({
            'error': 'Search failed'
        }, status=500)


@csrf_exempt
@require_http_methods(["POST"])
@login_required
def quick_leave_apply(request):
    """Quick leave application via AI assistant"""
    try:
        data = json.loads(request.body)
        
        leave_type = data.get('leave_type')
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        reason = data.get('reason', '')
        
        if not all([leave_type, start_date, end_date]):
            return JsonResponse({
                'error': 'Leave type, start date, and end date are required'
            }, status=400)
        
        # Use leave API handler
        leave_handler = LeaveAPIHandler(request.user)
        response = leave_handler.apply_leave(leave_type, start_date, end_date, reason)
        
        return JsonResponse(response)
        
    except json.JSONDecodeError:
        return JsonResponse({
            'error': 'Invalid JSON payload'
        }, status=400)
    except Exception as e:
        logger.error(f"Error in quick_leave_apply view: {str(e)}")
        return JsonResponse({
            'error': 'Leave application failed'
        }, status=500)


@login_required
def attendance_status(request):
    """Get current attendance status"""
    try:
        attendance_handler = AttendanceAPIHandler(request.user)
        
        # Get today's attendance report
        today = datetime.now().strftime('%Y-%m-%d')
        response = attendance_handler.get_attendance_report(
            employee_id=request.user.employee_get.id if hasattr(request.user, 'employee_get') else None,
            date_range=today
        )
        
        return JsonResponse(response)
        
    except Exception as e:
        logger.error(f"Error in attendance_status view: {str(e)}")
        return JsonResponse({
            'error': 'Failed to get attendance status'
        }, status=500)


@csrf_exempt
@require_http_methods(["POST"])
@login_required
def clock_action(request):
    """Handle clock in/out actions"""
    try:
        data = json.loads(request.body)
        action = data.get('action')  # 'in' or 'out'
        
        if action not in ['in', 'out']:
            return JsonResponse({
                'error': 'Action must be "in" or "out"'
            }, status=400)
        
        attendance_handler = AttendanceAPIHandler(request.user)
        
        if action == 'in':
            response = attendance_handler.clock_in()
        else:
            response = attendance_handler.clock_out()
        
        return JsonResponse(response)
        
    except json.JSONDecodeError:
        return JsonResponse({
            'error': 'Invalid JSON payload'
        }, status=400)
    except Exception as e:
        logger.error(f"Error in clock_action view: {str(e)}")
        return JsonResponse({
            'error': 'Clock action failed'
        }, status=500)


@login_required
def navigation_demo(request):
    """Demo page for AI assistant navigation functionality"""
    return render(request, 'ai_assistant/navigation_demo.html')


@login_required
def floating_widget_demo(request):
    """Demo page for floating AI assistant widget"""
    return render(request, 'ai_assistant/floating_widget_demo.html')


@csrf_exempt
@require_http_methods(["POST"])
@login_required
def file_upload(request):
    """Handle file uploads for AI assistant"""
    try:
        if 'file' not in request.FILES and 'audio' not in request.FILES:
            return JsonResponse({
                'success': False,
                'error': 'No file provided'
            }, status=400)

        file_obj = request.FILES.get('file') or request.FILES.get('audio')
        file_type = request.POST.get('type', 'file')

        # Validate file size (10MB limit)
        max_size = 10 * 1024 * 1024  # 10MB
        if file_obj.size > max_size:
            return JsonResponse({
                'success': False,
                'error': f'File too large. Maximum size is {max_size // 1024 // 1024}MB'
            }, status=400)

        # Process different file types
        response_data = {
            'success': True,
            'filename': file_obj.name,
            'size': file_obj.size,
            'type': file_type
        }

        if file_type == 'voice':
            # Process voice file
            response_data.update(process_voice_file(file_obj, request.user))
        else:
            # Process regular file
            response_data.update(process_uploaded_file(file_obj, request.user))

        return JsonResponse(response_data)

    except Exception as e:
        logger.error(f"Error in file_upload view: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': 'File upload failed'
        }, status=500)


def process_voice_file(audio_file, user):
    """Process uploaded voice file"""
    try:
        # Save audio file temporarily
        import tempfile
        import os

        with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as temp_file:
            for chunk in audio_file.chunks():
                temp_file.write(chunk)
            temp_path = temp_file.name

        # Here you can add speech-to-text processing
        # For now, return a placeholder
        transcription = "Voice message received (transcription not implemented)"

        # Clean up temp file
        os.unlink(temp_path)

        return {
            'transcription': transcription,
            'content': transcription
        }

    except Exception as e:
        logger.error(f"Error processing voice file: {str(e)}")
        return {
            'transcription': "Voice message received",
            'content': "Voice message received"
        }


def process_uploaded_file(uploaded_file, user):
    """Process uploaded file and extract content"""
    try:
        file_extension = uploaded_file.name.lower().split('.')[-1]
        content = ""

        if file_extension in ['txt', 'csv']:
            # Read text files
            content = uploaded_file.read().decode('utf-8')[:1000]  # Limit to 1000 chars

        elif file_extension == 'pdf':
            # Process PDF files (requires PyPDF2 or similar)
            content = "PDF file uploaded (content extraction not implemented)"

        elif file_extension in ['doc', 'docx']:
            # Process Word documents (requires python-docx)
            content = "Word document uploaded (content extraction not implemented)"

        elif file_extension in ['jpg', 'jpeg', 'png', 'gif']:
            # Process images (could add OCR or image analysis)
            content = f"Image file uploaded: {uploaded_file.name}"

        else:
            content = f"File uploaded: {uploaded_file.name}"

        return {
            'content': content,
            'extracted_text': content
        }

    except Exception as e:
        logger.error(f"Error processing uploaded file: {str(e)}")
        return {
            'content': f"File uploaded: {uploaded_file.name}",
            'extracted_text': ""
        }


@login_required
def ai_settings(request):
    """Get AI assistant settings"""
    try:
        settings = AIAssistantSettings.objects.filter(is_active=True).first()
        
        if not settings:
            return JsonResponse({
                'error': 'AI assistant settings not found'
            }, status=404)
        
        settings_data = {
            'ollama_model': settings.ollama_model,
            'rasa_server_url': settings.rasa_server_url,
            'confidence_threshold': settings.confidence_threshold,
            'enable_rasa': settings.enable_rasa,
            'enable_langchain': settings.enable_langchain,
            'enable_memory': settings.enable_memory,
            'max_chat_history': settings.max_chat_history
        }
        
        return JsonResponse(settings_data)
        
    except Exception as e:
        logger.error(f"Error in ai_settings view: {str(e)}")
        return JsonResponse({
            'error': 'Failed to get AI settings'
        }, status=500)


@login_required
def conversation_memory(request):
    """Get conversation memory for current user"""
    try:
        session_id = request.GET.get('session_id')
        
        if not session_id:
            return JsonResponse({
                'error': 'Session ID is required'
            }, status=400)
        
        memory = ConversationMemory.objects.filter(
            user=request.user,
            session_id=session_id,
            expires_at__gt=timezone.now()
        ).first()
        
        if memory:
            return JsonResponse({
                'context_data': memory.context_data,
                'last_accessed': memory.last_accessed.isoformat()
            })
        else:
            return JsonResponse({
                'context_data': {},
                'last_accessed': None
            })
        
    except Exception as e:
        logger.error(f"Error in conversation_memory view: {str(e)}")
        return JsonResponse({
            'error': 'Failed to get conversation memory'
        }, status=500)
