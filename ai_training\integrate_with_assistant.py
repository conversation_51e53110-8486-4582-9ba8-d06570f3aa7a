#!/usr/bin/env python3
"""
Eaglora HRMS AI Assistant Integration

This script integrates the trained AI models with the existing AI assistant,
providing enhanced natural language to API mapping capabilities.
"""

import os
import sys
import json
from datetime import datetime

def update_ai_assistant_views():
    """Update the AI assistant views.py with enhanced functionality"""
    
    views_file = "ai_assistant/views.py"
    
    if not os.path.exists(views_file):
        print(f"❌ AI assistant views file not found: {views_file}")
        return False
    
    print("🔄 Reading existing AI assistant views...")
    
    with open(views_file, 'r') as f:
        content = f.read()
    
    # Check if integration is already added
    if "ai_training.ai_integration" in content:
        print("✅ AI training integration already exists in views.py")
        return True
    
    # Add import statement
    import_statement = """
# AI Training Integration
try:
    from ai_training.ai_integration import ai_integration, predict_and_execute
    AI_TRAINING_AVAILABLE = True
except ImportError:
    AI_TRAINING_AVAILABLE = False
    print("⚠️  AI training models not available")
"""
    
    # Enhanced chat message function
    enhanced_function = '''
def enhanced_chat_message(request):
    """Enhanced chat message handler with trained AI models"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            user_message = data.get('message', '').strip()
            
            if not user_message:
                return JsonResponse({'error': 'Empty message'}, status=400)
            
            # Try AI-powered API prediction first
            if AI_TRAINING_AVAILABLE and ai_integration.is_loaded:
                try:
                    # Predict API call from natural language
                    prediction = ai_integration.predict_api_call(user_message)
                    
                    if prediction.get('success', False) and prediction.get('confidence', 0) > 0.3:
                        # Log the prediction for debugging
                        print(f"🤖 AI Prediction: {prediction['method']} {prediction['api_endpoint']}")
                        print(f"📊 Confidence: {prediction['confidence']:.3f}")
                        
                        # Execute the predicted API call
                        execution_result = ai_integration.execute_api_call(prediction)
                        
                        if execution_result.get('success', False):
                            # Format successful API response
                            api_data = execution_result.get('data', {})
                            
                            # Create human-readable response
                            response_text = format_api_response(prediction, api_data)
                            
                            return JsonResponse({
                                'response': response_text,
                                'type': 'api_result',
                                'api_call': {
                                    'endpoint': prediction['api_endpoint'],
                                    'method': prediction['method'],
                                    'module': prediction['module'],
                                    'confidence': prediction['confidence']
                                },
                                'data': api_data,
                                'success': True
                            })
                        else:
                            # API call failed, but we can still provide helpful info
                            return JsonResponse({
                                'response': f"I understand you want to {prediction['action']} {prediction['entity']}, but I encountered an issue accessing the data. Please try again or contact support.",
                                'type': 'api_error',
                                'api_call': prediction,
                                'error': execution_result.get('error'),
                                'success': False
                            })
                    
                except Exception as e:
                    print(f"❌ AI prediction error: {str(e)}")
                    # Continue to fallback processing
            
            # Fallback to regular chat processing
            return regular_chat_processing(request, user_message)
            
        except json.JSONDecodeError:
            return JsonResponse({'error': 'Invalid JSON'}, status=400)
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
    
    return JsonResponse({'error': 'Method not allowed'}, status=405)

def format_api_response(prediction, api_data):
    """Format API response into human-readable text"""
    action = prediction.get('action', 'retrieve')
    entity = prediction.get('entity', 'data')
    module = prediction.get('module', '')
    
    if isinstance(api_data, dict):
        if 'results' in api_data:
            count = len(api_data['results'])
            if count == 0:
                return f"I didn't find any {entity} matching your request."
            elif count == 1:
                return f"I found 1 {entity} for you."
            else:
                return f"I found {count} {entity} matching your request."
        elif 'count' in api_data:
            count = api_data['count']
            return f"I found {count} {entity} in the {module} system."
        elif api_data:
            return f"Here's the {entity} information you requested."
        else:
            return f"The {action} operation was completed successfully."
    elif isinstance(api_data, list):
        count = len(api_data)
        if count == 0:
            return f"I didn't find any {entity} matching your request."
        else:
            return f"I found {count} {entity} for you."
    else:
        return f"I've {action}d the {entity} as requested."

def regular_chat_processing(request, user_message):
    """Regular chat processing fallback"""
    # This is where the existing chat logic would go
    # For now, return a simple response
    return JsonResponse({
        'response': f"I received your message: '{user_message}'. The AI training system is working to understand your request better.",
        'type': 'fallback',
        'success': True
    })
'''
    
    # Find the right place to insert the code
    if "import json" not in content:
        content = "import json\n" + content
    
    # Add the import statement after existing imports
    import_position = content.find("from django.http import JsonResponse")
    if import_position != -1:
        # Find the end of the import section
        next_line = content.find("\n\n", import_position)
        if next_line != -1:
            content = content[:next_line] + import_statement + content[next_line:]
        else:
            content = content + import_statement
    else:
        content = import_statement + "\n" + content
    
    # Add the enhanced function
    content = content + "\n" + enhanced_function
    
    # Create backup
    backup_file = f"{views_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    with open(backup_file, 'w') as f:
        f.write(content)
    
    print(f"📁 Created backup: {backup_file}")
    
    # Write updated content
    with open(views_file, 'w') as f:
        f.write(content)
    
    print("✅ Successfully updated AI assistant views.py")
    return True

def update_ai_assistant_urls():
    """Update AI assistant URLs to include enhanced endpoint"""
    
    urls_file = "ai_assistant/urls.py"
    
    if not os.path.exists(urls_file):
        print(f"❌ AI assistant URLs file not found: {urls_file}")
        return False
    
    print("🔄 Reading AI assistant URLs...")
    
    with open(urls_file, 'r') as f:
        content = f.read()
    
    # Check if enhanced endpoint already exists
    if "enhanced_chat_message" in content:
        print("✅ Enhanced chat endpoint already exists in urls.py")
        return True
    
    # Add enhanced endpoint
    enhanced_url = """    # Enhanced AI chat with trained models
    path('api/enhanced-message/', views.enhanced_chat_message, name='enhanced_chat_message'),"""
    
    # Find the urlpatterns section
    urlpatterns_start = content.find("urlpatterns = [")
    if urlpatterns_start != -1:
        # Find the end of the first path entry
        first_path_end = content.find("\n", urlpatterns_start + len("urlpatterns = ["))
        if first_path_end != -1:
            content = content[:first_path_end] + "\n" + enhanced_url + content[first_path_end:]
        else:
            # Add before the closing bracket
            closing_bracket = content.find("]", urlpatterns_start)
            if closing_bracket != -1:
                content = content[:closing_bracket] + enhanced_url + "\n" + content[closing_bracket:]
    
    # Create backup
    backup_file = f"{urls_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    with open(backup_file, 'w') as f:
        f.write(content)
    
    print(f"📁 Created backup: {backup_file}")
    
    # Write updated content
    with open(urls_file, 'w') as f:
        f.write(content)
    
    print("✅ Successfully updated AI assistant urls.py")
    return True

def create_integration_test():
    """Create a test script for the integration"""
    
    test_content = '''#!/usr/bin/env python3
"""
Test script for AI assistant integration
"""

import requests
import json

def test_enhanced_chat():
    """Test the enhanced chat endpoint"""
    
    base_url = "http://localhost:8000"
    endpoint = "/ai-assistant/api/enhanced-message/"
    
    test_queries = [
        "find employee john",
        "show me all employees",
        "clock in now",
        "list leave requests",
        "get my payslip",
        "show company policies"
    ]
    
    print("🧪 Testing Enhanced AI Chat Integration")
    print("=" * 50)
    
    for query in test_queries:
        print(f"\\nTesting: {query}")
        
        try:
            response = requests.post(
                f"{base_url}{endpoint}",
                json={"message": query},
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"  ✅ Response: {data.get('response', 'No response')}")
                if data.get('api_call'):
                    api_call = data['api_call']
                    print(f"  🔗 API: {api_call.get('method')} {api_call.get('endpoint')}")
                    print(f"  📊 Confidence: {api_call.get('confidence', 0):.3f}")
            else:
                print(f"  ❌ Error: {response.status_code} - {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"  ❌ Request failed: {str(e)}")
        except Exception as e:
            print(f"  ❌ Error: {str(e)}")

if __name__ == "__main__":
    test_enhanced_chat()
'''
    
    test_file = "ai_training/test_integration.py"
    with open(test_file, 'w') as f:
        f.write(test_content)
    
    print(f"✅ Created integration test: {test_file}")
    return True

def main():
    """Main integration function"""
    print("🔗 Eaglora HRMS AI Assistant Integration")
    print("=" * 50)
    
    # Check if AI models are available
    models_dir = "ai_training/models"
    if not os.path.exists(models_dir):
        print("❌ AI models not found. Please run training first:")
        print("   cd ai_training && python run_training.py")
        return False
    
    print("✅ AI models found")
    
    # Update AI assistant views
    if not update_ai_assistant_views():
        print("❌ Failed to update AI assistant views")
        return False
    
    # Update AI assistant URLs
    if not update_ai_assistant_urls():
        print("❌ Failed to update AI assistant URLs")
        return False
    
    # Create integration test
    create_integration_test()
    
    print("\\n🎉 AI Assistant Integration Complete!")
    print("=" * 50)
    print("📋 What was done:")
    print("  ✅ Updated ai_assistant/views.py with enhanced chat handler")
    print("  ✅ Updated ai_assistant/urls.py with new endpoint")
    print("  ✅ Created integration test script")
    print("  ✅ Created backups of original files")
    
    print("\\n🚀 Next Steps:")
    print("1. Restart your Django server")
    print("2. Test the integration:")
    print("   python ai_training/test_integration.py")
    print("3. Use the enhanced endpoint:")
    print("   POST /ai-assistant/api/enhanced-message/")
    print("   Body: {'message': 'find employee john'}")
    
    print("\\n🎯 The AI assistant now understands natural language!")
    print("   Try queries like:")
    print("   - 'find employee john'")
    print("   - 'show me all employees'")
    print("   - 'clock in now'")
    print("   - 'list my leave requests'")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
