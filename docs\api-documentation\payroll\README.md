# Payroll Module API Documentation

## 📋 Overview

The Payroll module provides comprehensive APIs for managing payroll operations in the Eaglora HRMS system.

## 🔗 Base URL
```
/api/payroll/
```

## 📑 Available Endpoints

| Endpoint | Method | Description | File |
|----------|--------|-------------|------|
| `/contract/` | GET | List employee contracts | [contract-list.md](contract-list.md) |
| `/contract/` | POST | Create employee contract | [contract-create.md](contract-create.md) |
| `/contract/{id}/` | GET/PUT | Get or update employee contract | [contract-detail.md](contract-detail.md) |
| `/payslip/` | GET | List employee payslips | [payslip-list.md](payslip-list.md) |
| `/payslip/` | POST | Generate employee payslip | [payslip-create.md](payslip-create.md) |
| `/payslip/{id}/` | GET/PUT | Get or update payslip details | [payslip-detail.md](payslip-detail.md) |
| `/payslip-send-mail/{id}/` | POST | Send payslip via email | [payslip-send-mail.md](payslip-send-mail.md) |
| `/allowance/` | GET | List salary allowances | [allowance-list.md](allowance-list.md) |
| `/allowance/` | POST | Create salary allowance | [allowance-create.md](allowance-create.md) |
| `/allowance/{id}/` | GET/PUT | Get or update allowance details | [allowance-detail.md](allowance-detail.md) |
| `/deduction/` | GET | List salary deductions | [deduction-list.md](deduction-list.md) |
| `/deduction/` | POST | Create salary deduction | [deduction-create.md](deduction-create.md) |
| `/deduction/{id}/` | GET/PUT | Get or update deduction details | [deduction-detail.md](deduction-detail.md) |
| `/tax-bracket/` | GET | List tax brackets | [tax-bracket-list.md](tax-bracket-list.md) |
| `/tax-bracket/` | POST | Create tax bracket | [tax-bracket-create.md](tax-bracket-create.md) |
| `/tax-bracket/{id}/` | GET/PUT | Get or update tax bracket | [tax-bracket-detail.md](tax-bracket-detail.md) |
| `/loan/` | GET | List employee loans | [loan-list.md](loan-list.md) |
| `/loan/` | POST | Create employee loan | [loan-create.md](loan-create.md) |
| `/loan/{id}/` | GET/PUT | Get or update loan details | [loan-detail.md](loan-detail.md) |
| `/reimbursement/` | GET | List employee reimbursements | [reimbursement-list.md](reimbursement-list.md) |
| `/reimbursement/` | POST | Create reimbursement claim | [reimbursement-create.md](reimbursement-create.md) |
| `/reimbursement/{id}/` | GET/PUT | Get or update reimbursement details | [reimbursement-detail.md](reimbursement-detail.md) |
| `/bonus/` | GET | List employee bonuses | [bonus-list.md](bonus-list.md) |
| `/bonus/` | POST | Create employee bonus | [bonus-create.md](bonus-create.md) |
| `/bonus/{id}/` | GET/PUT | Get or update bonus details | [bonus-detail.md](bonus-detail.md) |

## 🔐 Authentication & Permissions

All endpoints require JWT authentication:

```http
Authorization: Bearer YOUR_JWT_TOKEN
```

## 🚀 Quick Start

### Basic Request Example
```bash
curl -X GET "/api/payroll/" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

## 📊 Use Cases

This module is commonly used for:
- **Allowance management**
- **Approval process**
- **Balance updates**
- **Benefit management**
- **Benefit setup**
- **Benefit updates**
- **Bonus management**
- **Bonus tracking**
- **Claim management**
- **Compliance**
- **Compliance configuration**
- **Contract details**
- **Contract management**
- **Contract setup**
- **Deduction management**
- **Email distribution**
- **Employee access**
- **Employee assistance**
- **Employee benefits**
- **Employee claims**
- **Employee communication**
- **Employee reimbursement**
- **Employee view**
- **Employment terms**
- **Expense claims**
- **Expense tracking**
- **Financial benefits**
- **Incentive programs**
- **Legal compliance**
- **Legal management**
- **Loan approval**
- **Loan deductions**
- **Loan management**
- **Loan tracking**
- **Monthly payroll**
- **New employee**
- **Payment tracking**
- **Payroll additions**
- **Payroll calculation**
- **Payroll deductions**
- **Payroll integration**
- **Payroll processing**
- **Payroll rules**
- **Payroll setup**
- **Payslip delivery**
- **Payslip details**
- **Payslip generation**
- **Payslip history**
- **Performance bonus**
- **Performance rewards**
- **Rate updates**
- **Reward tracking**
- **Rewards**
- **Salary adjustments**
- **Salary breakdown**
- **Salary components**
- **Salary processing**
- **Salary structure**
- **Tax configuration**
- **Tax deductions**
- **Tax management**
- **Tax setup**
- **Tax updates**
- **Terms updates**
- **Travel expenses**

## 🔄 Related Modules

- [Employee Module](../employee/README.md)
- [Attendance Module](../attendance/README.md)
- [Leave Module](../leave/README.md)
- [Payroll Module](../payroll/README.md)

---

**Navigate to specific endpoints using the links above.**
