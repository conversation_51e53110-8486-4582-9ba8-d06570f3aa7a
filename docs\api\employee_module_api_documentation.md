# Eaglora HRMS - Employee Module API Documentation

## Table of Contents
1. [Overview](#overview)
2. [Authentication](#authentication)
3. [Base URL](#base-url)
4. [Employee Endpoints](#employee-endpoints)
5. [Employee Work Information](#employee-work-information)
6. [Employee Bank Details](#employee-bank-details)
7. [Employee Types](#employee-types)
8. [Bulk Operations](#bulk-operations)
9. [Search and Filtering](#search-and-filtering)
10. [Error Handling](#error-handling)
11. [Data Models](#data-models)
12. [Examples](#examples)

---

## Overview

The Employee Module API provides comprehensive functionality for managing employees in the Eaglora HRMS system. This includes employee CRUD operations, work information management, bank details, search capabilities, and bulk operations.

### Key Features
- Employee profile management
- Work information tracking
- Bank details management
- Advanced search and filtering
- Bulk import/export operations
- Role-based access control
- Hierarchical employee relationships

---

## Authentication

All API endpoints require authentication. The system supports multiple authentication methods:

### Required Headers
```http
Authorization: Bearer <token>
Content-Type: application/json
```

### Permissions
- `employee.view_employee` - View employee data
- `employee.add_employee` - Create new employees
- `employee.change_employee` - Update employee information
- `employee.delete_employee` - Delete employees

---

## Base URL

```
https://your-domain.com/api/employee/
```

---

## Employee Endpoints

### 1. List All Employees

**GET** `/employees/`

Retrieves a paginated list of all employees with filtering and search capabilities.

#### Query Parameters
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `search` | string | Search by name | `?search=john` |
| `employee_first_name` | string | Filter by first name | `?employee_first_name=John` |
| `employee_last_name` | string | Filter by last name | `?employee_last_name=Doe` |
| `email` | string | Filter by email | `?email=<EMAIL>` |
| `badge_id` | string | Filter by badge ID | `?badge_id=EMP001` |
| `phone` | string | Filter by phone | `?phone=**********` |
| `country` | string | Filter by country | `?country=USA` |
| `gender` | string | Filter by gender | `?gender=male` |
| `is_active` | boolean | Filter by active status | `?is_active=true` |
| `department` | string | Filter by department | `?department=IT` |
| `groupby_field` | string | Group results by field | `?groupby_field=department` |
| `page` | integer | Page number | `?page=2` |

#### Response
```json
{
  "count": 150,
  "next": "https://api.example.com/api/employee/employees/?page=2",
  "previous": null,
  "results": [
    {
      "id": 1,
      "badge_id": "EMP001",
      "employee_user_id": 5,
      "employee_first_name": "John",
      "employee_last_name": "Doe",
      "employee_profile": "/media/employee/profile/john_doe.jpg",
      "email": "<EMAIL>",
      "phone": "+**********",
      "address": "123 Main St, City, State",
      "country": "USA",
      "state": "California",
      "city": "San Francisco",
      "zip": "94105",
      "dob": "1990-01-15",
      "gender": "male",
      "marital_status": "married",
      "is_active": true,
      "created_at": "2024-01-01T10:00:00Z",
      "job_position_name": "Software Engineer",
      "job_position_id": 3,
      "employee_work_info_id": 1,
      "employee_bank_details_id": 1
    }
  ]
}
```

### 2. Get Employee Details

**GET** `/employees/{id}/`

Retrieves detailed information for a specific employee.

#### Path Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| `id` | integer | Employee ID |

#### Response
```json
{
  "id": 1,
  "badge_id": "EMP001",
  "employee_user_id": 5,
  "employee_first_name": "John",
  "employee_last_name": "Doe",
  "employee_profile": "/media/employee/profile/john_doe.jpg",
  "email": "<EMAIL>",
  "phone": "+**********",
  "address": "123 Main St, City, State",
  "country": "USA",
  "state": "California",
  "city": "San Francisco",
  "zip": "94105",
  "dob": "1990-01-15",
  "gender": "male",
  "marital_status": "married",
  "blood_group": "O+",
  "emergency_contact": "+**********",
  "emergency_contact_name": "Jane Doe",
  "emergency_contact_relation": "Spouse",
  "is_active": true,
  "is_from_onboarding": false,
  "created_at": "2024-01-01T10:00:00Z",
  "updated_at": "2024-01-15T14:30:00Z",
  "job_position_name": "Software Engineer",
  "job_position_id": 3,
  "employee_work_info_id": 1,
  "employee_bank_details_id": 1
}
```

### 3. Create New Employee

**POST** `/employees/`

Creates a new employee record.

#### Required Permissions
- `employee.add_employee`

#### Request Body
```json
{
  "employee_first_name": "Jane",
  "employee_last_name": "Smith",
  "email": "<EMAIL>",
  "phone": "+**********",
  "address": "456 Oak Ave, City, State",
  "country": "USA",
  "state": "California",
  "city": "Los Angeles",
  "zip": "90210",
  "dob": "1992-05-20",
  "gender": "female",
  "marital_status": "single"
}
```

#### Response
```json
{
  "id": 2,
  "badge_id": "EMP002",
  "employee_first_name": "Jane",
  "employee_last_name": "Smith",
  "email": "<EMAIL>",
  "phone": "+**********",
  "is_active": true,
  "created_at": "2024-01-16T09:00:00Z"
}
```

### 4. Update Employee

**PUT** `/employees/{id}/`

Updates an existing employee record.

#### Required Permissions
- `employee.change_employee` OR
- User is the employee themselves OR
- User is the employee's manager

#### Request Body
```json
{
  "employee_first_name": "Jane",
  "employee_last_name": "Johnson",
  "phone": "+1234567892",
  "address": "789 Pine St, City, State"
}
```

#### Response
```json
{
  "id": 2,
  "badge_id": "EMP002",
  "employee_first_name": "Jane",
  "employee_last_name": "Johnson",
  "email": "<EMAIL>",
  "phone": "+1234567892",
  "address": "789 Pine St, City, State",
  "updated_at": "2024-01-16T15:30:00Z"
}
```

### 5. Delete Employee

**DELETE** `/employees/{id}/`

Deletes an employee record.

#### Required Permissions
- `employee.delete_employee`

#### Response
```json
{
  "message": "Employee deleted successfully"
}
```

---

## Employee Work Information

### 1. List Work Information

**GET** `/employee-work-information/`

Retrieves work information for employees.

#### Response
```json
{
  "count": 100,
  "results": [
    {
      "id": 1,
      "employee_id": 1,
      "job_position_id": 3,
      "job_position_name": "Software Engineer",
      "department_id": 2,
      "department_name": "Information Technology",
      "shift_id": 1,
      "shift_name": "Day Shift",
      "employee_type_id": 1,
      "employee_type_name": "Full Time",
      "reporting_manager_id": 5,
      "reporting_manager_first_name": "Mike",
      "reporting_manager_last_name": "Wilson",
      "work_type_id": 1,
      "work_type_name": "Office",
      "company_id": 1,
      "company_name": "Eaglora Inc",
      "date_joining": "2024-01-01",
      "contract_end_date": null,
      "basic_salary": 75000,
      "salary_hour": 36,
      "location": "San Francisco Office",
      "email": "<EMAIL>",
      "mobile": "+**********",
      "tags": [
        {
          "id": 1,
          "title": "Senior Developer",
          "color": "#007bff"
        }
      ]
    }
  ]
}
```

### 2. Create Work Information

**POST** `/employee-work-information/`

Creates work information for an employee.

#### Request Body
```json
{
  "employee_id": 2,
  "job_position_id": 4,
  "department_id": 3,
  "shift_id": 1,
  "employee_type_id": 1,
  "reporting_manager_id": 5,
  "work_type_id": 1,
  "company_id": 1,
  "date_joining": "2024-01-16",
  "basic_salary": 65000,
  "salary_hour": 31,
  "location": "Los Angeles Office",
  "email": "<EMAIL>",
  "mobile": "+**********"
}
```

### 3. Update Work Information

**PUT** `/employee-work-information/{id}/`

Updates work information for an employee.

---

## Employee Bank Details

### 1. Get Bank Details

**GET** `/employee-bank-details/{id}/`

Retrieves bank details for a specific employee.

#### Response
```json
{
  "id": 1,
  "employee_id": 1,
  "bank_name": "Chase Bank",
  "account_number": "****1234",
  "routing_number": "*********",
  "account_type": "checking",
  "branch_name": "Downtown Branch",
  "ifsc_code": "CHAS0000123"
}
```

---

## Employee Types

### 1. List Employee Types

**GET** `/employee-type/`

Retrieves all employee types.

#### Response
```json
{
  "results": [
    {
      "id": 1,
      "employee_type": "Full Time",
      "is_active": true
    },
    {
      "id": 2,
      "employee_type": "Part Time",
      "is_active": true
    },
    {
      "id": 3,
      "employee_type": "Contract",
      "is_active": true
    }
  ]
}
```

---

## Bulk Operations

### 1. Bulk Update Employees

**PUT** `/employee-bulk-update/`

Updates multiple employees at once.

#### Required Permissions
- `employee.change_employee`

#### Request Body
```json
{
  "employees": [
    {
      "id": 1,
      "employee_first_name": "John Updated",
      "phone": "+**********"
    },
    {
      "id": 2,
      "employee_last_name": "Smith Updated",
      "address": "New Address"
    }
  ]
}
```

### 2. Export Work Information

**GET** `/employee-work-info-export/`

Exports employee work information to Excel format.

#### Query Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| `format` | string | Export format (excel, csv) |
| `employee_ids` | string | Comma-separated employee IDs |

### 3. Import Work Information

**POST** `/employee-work-info-import/`

Imports employee work information from Excel file.

#### Request Body (multipart/form-data)
```
file: <excel_file>
```

---

## Search and Filtering

### Advanced Search

**GET** `/list/employees/`

Provides advanced search capabilities with pagination.

#### Query Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| `search` | string | Search in name fields |
| `page_size` | integer | Number of results per page (default: 13) |

#### Response
```json
{
  "count": 25,
  "next": "https://api.example.com/api/employee/list/employees/?page=2",
  "previous": null,
  "results": [
    {
      "id": 1,
      "employee_first_name": "John",
      "employee_last_name": "Doe",
      "email": "<EMAIL>",
      "job_position_name": "Software Engineer",
      "employee_work_info_id": 1,
      "employee_profile": "/media/employee/profile/john_doe.jpg",
      "employee_bank_details_id": 1
    }
  ]
}
```

---

## Error Handling

### Common HTTP Status Codes

| Status Code | Description |
|-------------|-------------|
| 200 | Success |
| 201 | Created |
| 400 | Bad Request |
| 401 | Unauthorized |
| 403 | Forbidden |
| 404 | Not Found |
| 500 | Internal Server Error |

### Error Response Format

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "email": ["This field is required."],
      "phone": ["Enter a valid phone number."]
    }
  }
}
```

---

## Data Models

### Employee Model Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `id` | integer | Auto | Primary key |
| `badge_id` | string | Auto | Auto-generated badge ID |
| `employee_user_id` | integer | No | Related Django user |
| `employee_first_name` | string | Yes | First name |
| `employee_last_name` | string | No | Last name |
| `employee_profile` | file | No | Profile image |
| `email` | email | Yes | Email address (unique) |
| `phone` | string | Yes | Phone number |
| `address` | text | No | Address |
| `country` | string | No | Country |
| `state` | string | No | State |
| `city` | string | No | City |
| `zip` | string | No | ZIP code |
| `dob` | date | No | Date of birth |
| `gender` | choice | No | Gender (male/female/other) |
| `marital_status` | choice | No | Marital status |
| `blood_group` | string | No | Blood group |
| `emergency_contact` | string | No | Emergency contact number |
| `emergency_contact_name` | string | No | Emergency contact name |
| `emergency_contact_relation` | string | No | Relation to emergency contact |
| `is_active` | boolean | No | Active status (default: true) |
| `is_from_onboarding` | boolean | No | From onboarding process |
| `created_at` | datetime | Auto | Creation timestamp |
| `updated_at` | datetime | Auto | Last update timestamp |

---

## Examples

### Complete Employee Creation Flow

```bash
# 1. Create employee
curl -X POST "https://api.example.com/api/employee/employees/" \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "employee_first_name": "Alice",
    "employee_last_name": "Johnson",
    "email": "<EMAIL>",
    "phone": "+1234567893",
    "country": "USA",
    "gender": "female"
  }'

# 2. Add work information
curl -X POST "https://api.example.com/api/employee/employee-work-information/" \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "employee_id": 3,
    "job_position_id": 2,
    "department_id": 1,
    "date_joining": "2024-01-20",
    "basic_salary": 70000
  }'

# 3. Search for employees
curl -X GET "https://api.example.com/api/employee/employees/?search=alice&is_active=true" \
  -H "Authorization: Bearer your-token"
```

### Filtering Examples

```bash
# Filter by department and active status
GET /api/employee/employees/?department=IT&is_active=true

# Search by name with pagination
GET /api/employee/employees/?search=john&page=2

# Group by department
GET /api/employee/employees/?groupby_field=employee_work_info__department_id

# Filter by multiple criteria
GET /api/employee/employees/?country=USA&gender=female&employee_work_info__job_position_id=3
```

---

## Rate Limiting

API requests are rate-limited to prevent abuse:
- **Authenticated users**: 1000 requests per hour
- **Anonymous users**: 100 requests per hour

---

## Changelog

### Version 1.0.0
- Initial API release
- Basic CRUD operations
- Search and filtering
- Bulk operations
- Work information management

---

## Employee Selector API

### Get Employee Selector

**GET** `/employee-selector/`

Retrieves employees based on user permissions and hierarchy.

#### Response
```json
{
  "count": 10,
  "results": [
    {
      "id": 1,
      "employee_first_name": "John",
      "employee_last_name": "Doe",
      "email": "<EMAIL>",
      "employee_profile": "/media/employee/profile/john_doe.jpg"
    }
  ]
}
```

---

## Disciplinary Actions

### 1. List Disciplinary Actions

**GET** `/disciplinary-action/`

Retrieves disciplinary actions for employees.

#### Query Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| `employee_id` | integer | Filter by employee |
| `action_type` | string | Filter by action type |
| `date_from` | date | Filter from date |
| `date_to` | date | Filter to date |

#### Response
```json
{
  "results": [
    {
      "id": 1,
      "employee_id": 1,
      "action_type": "warning",
      "title": "Late Arrival",
      "description": "Employee was late for 3 consecutive days",
      "action_date": "2024-01-15",
      "created_by": 5,
      "is_active": true
    }
  ]
}
```

### 2. Create Disciplinary Action

**POST** `/disciplinary-action/`

Creates a new disciplinary action.

#### Required Permissions
- `employee.add_disciplinaryaction`

#### Request Body
```json
{
  "employee_id": 1,
  "action_type": "warning",
  "title": "Attendance Issue",
  "description": "Multiple late arrivals in the past week",
  "action_date": "2024-01-16"
}
```

---

## Document Management

### 1. List Employee Documents

**GET** `/documents/`

Retrieves documents associated with employees.

#### Query Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| `employee_id` | integer | Filter by employee |
| `document_type` | string | Filter by document type |

#### Response
```json
{
  "results": [
    {
      "id": 1,
      "employee_id": 1,
      "title": "Employment Contract",
      "document_type": "contract",
      "file": "/media/documents/contract_john_doe.pdf",
      "uploaded_date": "2024-01-01T10:00:00Z",
      "is_active": true
    }
  ]
}
```

### 2. Document Requests

**GET** `/document-requests/`

Retrieves document requests from employees.

#### Response
```json
{
  "results": [
    {
      "id": 1,
      "employee_id": 1,
      "document_type": "experience_letter",
      "request_date": "2024-01-15",
      "status": "pending",
      "reason": "Job application requirement"
    }
  ]
}
```

---

## Policies

### 1. List Policies

**GET** `/policies/`

Retrieves company policies.

#### Response
```json
{
  "results": [
    {
      "id": 1,
      "title": "Remote Work Policy",
      "description": "Guidelines for remote work arrangements",
      "effective_date": "2024-01-01",
      "is_active": true,
      "document": "/media/policies/remote_work_policy.pdf"
    }
  ]
}
```

---

## Advanced Features

### Hierarchical Employee Access

The API implements hierarchical access control:

1. **Employees** can only access their own data
2. **Managers** can access their subordinates' data
3. **HR Users** can access all employee data
4. **Superusers** have unrestricted access

### Permission Matrix

| Role | View All | View Own | View Subordinates | Create | Update | Delete |
|------|----------|----------|-------------------|--------|--------|--------|
| Employee | ❌ | ✅ | ❌ | ❌ | Own Only | ❌ |
| Manager | ❌ | ✅ | ✅ | ❌ | Own + Subordinates | ❌ |
| HR User | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| Admin | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |

### Data Validation Rules

1. **Email**: Must be unique across all employees
2. **Badge ID**: Auto-generated, cannot be manually set
3. **Phone**: Must be valid phone number format
4. **Date of Birth**: Cannot be in the future
5. **Joining Date**: Cannot be before company establishment date
6. **Salary**: Must be positive number

### Audit Trail

All employee data changes are automatically logged:
- Who made the change
- What was changed
- When the change occurred
- Previous and new values

---

## SDK Examples

### Python SDK Example

```python
import requests

class EagloraEmployeeAPI:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }

    def get_employees(self, **filters):
        """Get list of employees with optional filters"""
        response = requests.get(
            f"{self.base_url}/api/employee/employees/",
            headers=self.headers,
            params=filters
        )
        return response.json()

    def create_employee(self, employee_data):
        """Create a new employee"""
        response = requests.post(
            f"{self.base_url}/api/employee/employees/",
            headers=self.headers,
            json=employee_data
        )
        return response.json()

    def update_employee(self, employee_id, update_data):
        """Update an existing employee"""
        response = requests.put(
            f"{self.base_url}/api/employee/employees/{employee_id}/",
            headers=self.headers,
            json=update_data
        )
        return response.json()

# Usage
api = EagloraEmployeeAPI('https://your-domain.com', 'your-token')

# Get all active employees
employees = api.get_employees(is_active=True)

# Create new employee
new_employee = api.create_employee({
    'employee_first_name': 'John',
    'employee_last_name': 'Doe',
    'email': '<EMAIL>',
    'phone': '+**********'
})
```

### JavaScript SDK Example

```javascript
class EagloraEmployeeAPI {
    constructor(baseUrl, token) {
        this.baseUrl = baseUrl;
        this.headers = {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
    }

    async getEmployees(filters = {}) {
        const params = new URLSearchParams(filters);
        const response = await fetch(
            `${this.baseUrl}/api/employee/employees/?${params}`,
            { headers: this.headers }
        );
        return response.json();
    }

    async createEmployee(employeeData) {
        const response = await fetch(
            `${this.baseUrl}/api/employee/employees/`,
            {
                method: 'POST',
                headers: this.headers,
                body: JSON.stringify(employeeData)
            }
        );
        return response.json();
    }
}

// Usage
const api = new EagloraEmployeeAPI('https://your-domain.com', 'your-token');

// Get employees with search
api.getEmployees({ search: 'john', is_active: true })
    .then(data => console.log(data));
```

---

## Testing

### Unit Test Examples

```python
import pytest
from django.test import TestCase
from rest_framework.test import APIClient
from django.contrib.auth.models import User
from employee.models import Employee

class EmployeeAPITestCase(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass'
        )
        self.client.force_authenticate(user=self.user)

    def test_create_employee(self):
        data = {
            'employee_first_name': 'Test',
            'employee_last_name': 'User',
            'email': '<EMAIL>',
            'phone': '+**********'
        }
        response = self.client.post('/api/employee/employees/', data)
        self.assertEqual(response.status_code, 201)
        self.assertEqual(Employee.objects.count(), 1)

    def test_get_employee_list(self):
        Employee.objects.create(
            employee_first_name='John',
            employee_last_name='Doe',
            email='<EMAIL>',
            phone='+**********'
        )
        response = self.client.get('/api/employee/employees/')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data['results']), 1)
```

---

## Performance Considerations

### Optimization Tips

1. **Use Pagination**: Always use pagination for large datasets
2. **Selective Fields**: Use field selection to reduce payload size
3. **Caching**: Implement caching for frequently accessed data
4. **Database Indexing**: Ensure proper indexing on filter fields
5. **Bulk Operations**: Use bulk endpoints for multiple operations

### Query Optimization

```python
# Good: Use select_related for foreign keys
employees = Employee.objects.select_related(
    'employee_work_info__job_position_id',
    'employee_work_info__department_id'
).all()

# Good: Use prefetch_related for many-to-many
employees = Employee.objects.prefetch_related(
    'employee_work_info__tags'
).all()
```

---

## Security Best Practices

1. **Authentication**: Always use proper authentication
2. **Authorization**: Implement role-based access control
3. **Input Validation**: Validate all input data
4. **Rate Limiting**: Implement rate limiting to prevent abuse
5. **HTTPS**: Always use HTTPS in production
6. **Data Sanitization**: Sanitize all user inputs
7. **Audit Logging**: Log all sensitive operations

---

## Troubleshooting

### Common Issues

1. **403 Forbidden**: Check user permissions
2. **404 Not Found**: Verify employee ID exists
3. **400 Bad Request**: Check request data format
4. **500 Internal Server Error**: Check server logs

### Debug Mode

Enable debug mode for detailed error messages:

```python
# settings.py
DEBUG = True  # Only in development
```

---

For more information or support, please contact the development team or refer to the main Eaglora HRMS documentation.

**API Version**: 1.0.0
**Last Updated**: January 2024
**Support**: <EMAIL>
