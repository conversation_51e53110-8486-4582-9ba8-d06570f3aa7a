version: "3.1"

intents:
  - greet
  - goodbye
  - affirm
  - deny
  - get_employee_profile
  - search_employee
  - clock_in
  - clock_out
  - check_attendance
  - apply_leave
  - check_leave_balance
  - check_leave_status
  - approve_leave
  - reject_leave
  - cancel_leave
  - get_payroll_info
  - check_salary
  - download_payslip
  - asset_request
  - check_asset_status
  - return_asset
  - help_desk_ticket
  - check_ticket_status
  - update_profile
  - change_password
  - get_company_info
  - get_department_info
  - get_job_positions
  - get_holidays
  - get_announcements
  - get_notifications
  - mark_notification_read
  - get_team_info
  - get_manager_info
  - get_subordinates
  - request_overtime
  - check_overtime_status
  - submit_timesheet
  - get_work_schedule
  - request_shift_change
  - check_shift_schedule
  - get_performance_review
  - submit_feedback
  - get_training_info
  - enroll_training
  - check_training_status
  - get_benefits_info
  - update_emergency_contact
  - update_bank_details
  - get_tax_info
  - submit_expense_claim
  - check_expense_status
  - book_meeting_room
  - check_room_availability
  - cancel_booking
  - get_org_chart
  - get_policy_documents
  - report_issue
  - get_help
  - ask_question
  - provide_feedback
  - request_information
  - schedule_meeting
  - reschedule_meeting
  - cancel_meeting
  - get_calendar
  - set_reminder

entities:
  - employee_name
  - badge_id
  - email
  - department
  - employee_id
  - date
  - start_date
  - end_date
  - duration
  - time_period
  - leave_type
  - month
  - year
  - amount
  - currency
  - asset_type
  - ticket_id
  - meeting_room
  - time
  - location
  - phone_number
  - address
  - job_title
  - salary
  - percentage
  - number
  - file_name
  - document_type
  - priority
  - status
  - category
  - type
  - reason
  - description
  - comment
  - feedback
  - rating
  - score
  - level
  - grade
  - rank
  - position
  - role
  - responsibility
  - skill
  - qualification
  - experience
  - certification
  - training
  - course
  - program
  - project
  - task
  - goal
  - objective
  - target
  - milestone
  - deadline
  - schedule
  - calendar
  - event
  - appointment
  - meeting
  - conference
  - workshop
  - seminar
  - presentation
  - report
  - document
  - file
  - folder

slots:
  employee_name:
    type: text
    influence_conversation: true
    mappings:
    - type: from_entity
      entity: employee_name
  
  employee_id:
    type: text
    influence_conversation: true
    mappings:
    - type: from_entity
      entity: employee_id
  
  leave_type:
    type: text
    influence_conversation: true
    mappings:
    - type: from_entity
      entity: leave_type
  
  start_date:
    type: text
    influence_conversation: true
    mappings:
    - type: from_entity
      entity: start_date
  
  end_date:
    type: text
    influence_conversation: true
    mappings:
    - type: from_entity
      entity: end_date
  
  date_range:
    type: text
    influence_conversation: true
    mappings:
    - type: from_entity
      entity: date_range
  
  session_started_metadata:
    type: any
    mappings:
    - type: from_entity
      entity: session_started_metadata

responses:
  utter_greet:
  - text: "Hello! I'm your HR assistant. How can I help you today?"
  - text: "Hi there! I'm here to help with your HR needs. What can I do for you?"
  - text: "Welcome! I can help you with employee information, leave requests, attendance, and more. What would you like to know?"

  utter_goodbye:
  - text: "Goodbye! Have a great day!"
  - text: "See you later! Feel free to ask me anything anytime."
  - text: "Take care! I'm always here when you need HR assistance."

  utter_iamabot:
  - text: "I am your AI HR assistant, designed to help you with all your HR-related queries and tasks."

  utter_ask_employee_name:
  - text: "Which employee are you asking about? Please provide their name or employee ID."
  - text: "Could you tell me the employee's name or ID?"

  utter_ask_leave_type:
  - text: "What type of leave would you like to apply for? (e.g., Annual Leave, Sick Leave, Personal Leave)"
  - text: "Which leave type do you need? Please specify the type of leave."

  utter_ask_start_date:
  - text: "When would you like your leave to start? Please provide the start date."
  - text: "What's the start date for your leave request?"

  utter_ask_end_date:
  - text: "When will your leave end? Please provide the end date."
  - text: "What's the end date for your leave?"

  utter_ask_date_range:
  - text: "For which time period would you like to check? Please specify the date range."
  - text: "Which dates are you interested in? Please provide the date range."

  utter_default:
  - text: "I'm sorry, I didn't understand that. Could you please rephrase your question?"
  - text: "I'm not sure I understand. Can you try asking in a different way?"
  - text: "Could you please clarify what you're looking for? I'm here to help with HR-related queries."

  utter_help:
  - text: "I can help you with:\n• Employee profiles and information\n• Leave applications and balance\n• Attendance tracking and reports\n• Payroll information\n• HR policies\n• Document requests\n• And much more!\n\nJust ask me what you need!"

actions:
  # Original actions (now with navigation responses)
  - action_get_employee_profile
  - action_search_employee
  - action_apply_leave
  - action_check_leave_balance
  - action_view_leave_requests
  - action_check_attendance
  - action_clock_in
  - action_clock_out
  - action_view_attendance_report
  - action_get_payroll_info
  - action_ask_hr_policy
  - action_request_document
  - action_update_personal_info
  - action_view_team_info
  - action_schedule_meeting
  - action_get_holiday_list
  - action_default_fallback
  # Navigation-specific actions
  - action_navigate_to_attendance
  - action_navigate_to_leave_balance
  - action_navigate_to_leave_application
  - action_navigate_to_employee_profile
  - action_navigate_to_payroll
  - action_navigate_to_asset_request
  - action_navigate_to_help_desk
  - action_navigate_to_company_info
  - action_navigate_to_holidays
  - action_asset_request
  - action_help_desk_ticket
  - action_get_company_info
  - action_get_holidays

session_config:
  session_expiration_time: 60
  carry_over_slots_to_new_session: true
