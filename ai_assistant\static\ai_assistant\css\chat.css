/* AI Assistant Chat Interface Styles */

.chat-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

#chat-messages {
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    padding: 1rem;
    overflow-y: auto;
    scroll-behavior: smooth;
}

.message {
    margin-bottom: 1rem;
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message-content {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.user-message .message-content {
    flex-direction: row-reverse;
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.bot-message .avatar {
    background-color: #007bff;
    color: white;
}

.user-message .avatar {
    background-color: #28a745;
    color: white;
}

.content {
    max-width: 70%;
    padding: 0.75rem 1rem;
    border-radius: 1rem;
    position: relative;
}

.bot-message .content {
    background-color: white;
    border: 1px solid #dee2e6;
    border-bottom-left-radius: 0.25rem;
}

.user-message .content {
    background-color: #007bff;
    color: white;
    border-bottom-right-radius: 0.25rem;
}

.content p {
    margin-bottom: 0.5rem;
}

.content p:last-child {
    margin-bottom: 0;
}

.content ul, .content ol {
    margin-bottom: 0.5rem;
    padding-left: 1.5rem;
}

.content li {
    margin-bottom: 0.25rem;
}

.message-actions {
    margin-top: 0.5rem;
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.action-button {
    padding: 0.25rem 0.75rem;
    border: 1px solid #007bff;
    background-color: transparent;
    color: #007bff;
    border-radius: 1rem;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-button:hover {
    background-color: #007bff;
    color: white;
}

.quick-actions {
    background-color: #f8f9fa;
}

.quick-action-btn {
    font-size: 0.8rem;
    padding: 0.5rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
}

.quick-action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.message-input {
    background-color: white;
}

#message-input {
    border-radius: 1.5rem;
    padding: 0.75rem 1rem;
    border: 1px solid #dee2e6;
}

#message-input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

#send-button {
    border-radius: 50%;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 0.5rem;
}

.typing-indicator {
    color: #6c757d;
    font-style: italic;
}

.confidence-badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    margin-left: 0.5rem;
}

.confidence-high {
    background-color: #d4edda;
    color: #155724;
}

.confidence-medium {
    background-color: #fff3cd;
    color: #856404;
}

.confidence-low {
    background-color: #f8d7da;
    color: #721c24;
}

.error-message {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    padding: 0.75rem;
    border-radius: 0.375rem;
    margin-bottom: 1rem;
}

.success-message {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    padding: 0.75rem;
    border-radius: 0.375rem;
    margin-bottom: 1rem;
}

.api-response {
    background-color: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 0.375rem;
    padding: 0.75rem;
    margin-top: 0.5rem;
}

.api-response-header {
    font-weight: 600;
    color: #0056b3;
    margin-bottom: 0.5rem;
}

.api-response-data {
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    background-color: #f8f9fa;
    padding: 0.5rem;
    border-radius: 0.25rem;
    overflow-x: auto;
}

.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-online {
    background-color: #28a745;
}

.status-offline {
    background-color: #dc3545;
}

.status-processing {
    background-color: #ffc107;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .content {
        max-width: 85%;
    }
    
    .quick-action-btn {
        font-size: 0.7rem;
        padding: 0.4rem;
    }
    
    #chat-messages {
        max-height: 50vh;
    }
}

/* Scrollbar Styling */
#chat-messages::-webkit-scrollbar {
    width: 6px;
}

#chat-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

#chat-messages::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

#chat-messages::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Chat History Styling */
#chat-history .list-group-item {
    border-left: none;
    border-right: none;
    border-top: 1px solid #dee2e6;
    border-bottom: none;
}

#chat-history .list-group-item:last-child {
    border-bottom: 1px solid #dee2e6;
}

#chat-history .list-group-item:hover {
    background-color: #f8f9fa;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.btn-loading {
    position: relative;
}

.btn-loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    margin: auto;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Navigation indicator styles */
.navigation-indicator {
    opacity: 0.8;
}

.navigation-loading {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background-color: #e3f2fd;
    border-radius: 1rem;
    color: #1976d2;
    font-size: 0.9rem;
}

.navigation-loading .spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #e3f2fd;
    border-top: 2px solid #1976d2;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Responsive design */
@media (max-width: 768px) {
    .chat-container {
        height: calc(100vh - 60px);
    }

    .message-bubble {
        max-width: 85%;
    }

    .quick-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .quick-action-btn {
        width: 100%;
    }
}
