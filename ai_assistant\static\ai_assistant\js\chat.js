/**
 * AI Assistant Chat Interface JavaScript
 * Handles real-time chat communication with the AI assistant
 */

class ChatInterface {
    constructor(config) {
        this.config = config;
        this.sessionId = config.sessionId || this.generateSessionId();
        this.isProcessing = false;
        
        this.initializeElements();
        this.bindEvents();
        this.loadChatHistory();
    }
    
    initializeElements() {
        this.chatMessages = document.getElementById('chat-messages');
        this.messageInput = document.getElementById('message-input');
        this.sendButton = document.getElementById('send-button');
        this.chatForm = document.getElementById('chat-form');
        this.typingIndicator = document.querySelector('.typing-indicator');
        this.statusIndicator = document.getElementById('status-indicator');
        this.quickActionButtons = document.querySelectorAll('.quick-action-btn');
    }
    
    bindEvents() {
        // Form submission
        this.chatForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.sendMessage();
        });
        
        // Quick action buttons
        this.quickActionButtons.forEach(button => {
            button.addEventListener('click', () => {
                const message = button.getAttribute('data-message');
                this.messageInput.value = message;
                this.sendMessage();
            });
        });
        
        // Enter key handling
        this.messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        
        // Auto-resize input
        this.messageInput.addEventListener('input', () => {
            this.autoResizeInput();
        });
    }
    
    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    async sendMessage() {
        const message = this.messageInput.value.trim();
        
        if (!message || this.isProcessing) {
            return;
        }
        
        this.isProcessing = true;
        this.setUIState('processing');
        
        // Add user message to chat
        this.addMessage(message, 'user');
        
        // Clear input
        this.messageInput.value = '';
        this.autoResizeInput();
        
        try {
            const response = await this.callAPI(message);
            this.handleAPIResponse(response);
        } catch (error) {
            console.error('Error sending message:', error);
            this.addMessage('Sorry, there was an error processing your request. Please try again.', 'bot', 'error');
        } finally {
            this.isProcessing = false;
            this.setUIState('ready');
        }
    }
    
    async callAPI(message) {
        const response = await fetch(this.config.apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.config.csrfToken
            },
            body: JSON.stringify({
                message: message,
                session_id: this.sessionId
            })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    }
    
    handleAPIResponse(response) {
        if (response.error) {
            this.addMessage(response.error, 'bot', 'error');
            return;
        }
        
        // Add bot response
        this.addMessage(response.message, 'bot', 'success', {
            confidence: response.confidence,
            intent: response.intent,
            action: response.action,
            url: response.url,
            filters: response.filters,
            data: response.data
        });

        // Show navigation indicator for navigate actions
        if (response.action === 'navigate' && response.url) {
            this.showNavigationIndicator(response.url, response.filters);
        }

        // Handle specific actions
        this.handleAction(response);
    }
    
    handleAction(response) {
        switch (response.action) {
            case 'navigate':
                // Direct navigation to the specified URL
                if (response.url) {
                    const targetUrl = this.buildUrlWithFilters(response.url, response.filters);
                    // Add a small delay for better UX
                    setTimeout(() => {
                        window.location.href = targetUrl;
                    }, 1000);
                }
                break;

            case 'redirect':
                // Open in new tab/window
                if (response.url) {
                    const targetUrl = this.buildUrlWithFilters(response.url, response.filters);
                    window.open(targetUrl, '_blank');
                }
                break;

            case 'search':
            case 'show_profile':
            case 'show_search_results':
            case 'show_attendance':
            case 'show_leave_balance':
                if (response.url) {
                    this.addActionButton('View Details', () => {
                        const targetUrl = this.buildUrlWithFilters(response.url, response.filters);
                        window.open(targetUrl, '_blank');
                    });
                }
                break;

            case 'leave_applied':
                this.addActionButton('View Leave Requests', () => {
                    window.open('/leave/request/', '_blank');
                });
                break;

            case 'clock_in_success':
            case 'clock_out_success':
                this.addActionButton('View Attendance', () => {
                    window.open('/attendance/', '_blank');
                });
                break;
        }
    }

    buildUrlWithFilters(url, filters = {}) {
        /**
         * Build URL with query parameters from filters
         */
        if (!filters || Object.keys(filters).length === 0) {
            return url;
        }

        const urlObj = new URL(url, window.location.origin);
        Object.keys(filters).forEach(key => {
            if (filters[key] !== null && filters[key] !== undefined && filters[key] !== '') {
                urlObj.searchParams.set(key, filters[key]);
            }
        });

        return urlObj.pathname + urlObj.search;
    }

    showNavigationIndicator(url, filters = {}) {
        /**
         * Show a visual indicator that navigation is happening
         */
        const indicatorDiv = document.createElement('div');
        indicatorDiv.className = 'message bot-message navigation-indicator';
        indicatorDiv.innerHTML = `
            <div class="message-content">
                <div class="navigation-loading">
                    <div class="spinner"></div>
                    <span>Navigating...</span>
                </div>
            </div>
        `;

        this.messagesContainer.appendChild(indicatorDiv);
        this.scrollToBottom();

        // Remove indicator after navigation
        setTimeout(() => {
            if (indicatorDiv.parentNode) {
                indicatorDiv.parentNode.removeChild(indicatorDiv);
            }
        }, 1500);
    }

    addMessage(text, sender, type = 'normal', metadata = {}) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;
        
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        
        // Avatar
        const avatar = document.createElement('div');
        avatar.className = 'avatar';
        avatar.innerHTML = sender === 'user' ? 
            '<i class="fas fa-user"></i>' : 
            '<i class="fas fa-robot"></i>';
        
        // Content
        const content = document.createElement('div');
        content.className = 'content';
        
        // Message text
        const textElement = document.createElement('p');
        textElement.innerHTML = this.formatMessage(text);
        content.appendChild(textElement);
        
        // Add confidence badge for bot messages
        if (sender === 'bot' && metadata.confidence !== undefined) {
            const confidenceBadge = this.createConfidenceBadge(metadata.confidence);
            content.appendChild(confidenceBadge);
        }
        
        // Add metadata display for debugging (if enabled)
        if (metadata.intent && this.config.debug) {
            const metadataDiv = document.createElement('div');
            metadataDiv.className = 'message-metadata';
            metadataDiv.innerHTML = `<small>Intent: ${metadata.intent}</small>`;
            content.appendChild(metadataDiv);
        }
        
        messageContent.appendChild(avatar);
        messageContent.appendChild(content);
        messageDiv.appendChild(messageContent);
        
        // Add to chat
        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
        
        return messageDiv;
    }
    
    addActionButton(text, callback) {
        const lastMessage = this.chatMessages.lastElementChild;
        if (!lastMessage || !lastMessage.classList.contains('bot-message')) {
            return;
        }
        
        const content = lastMessage.querySelector('.content');
        let actionsDiv = content.querySelector('.message-actions');
        
        if (!actionsDiv) {
            actionsDiv = document.createElement('div');
            actionsDiv.className = 'message-actions';
            content.appendChild(actionsDiv);
        }
        
        const button = document.createElement('button');
        button.className = 'action-button';
        button.textContent = text;
        button.addEventListener('click', callback);
        
        actionsDiv.appendChild(button);
    }
    
    createConfidenceBadge(confidence) {
        const badge = document.createElement('span');
        badge.className = 'confidence-badge';
        
        if (confidence >= 0.8) {
            badge.classList.add('confidence-high');
        } else if (confidence >= 0.6) {
            badge.classList.add('confidence-medium');
        } else {
            badge.classList.add('confidence-low');
        }
        
        badge.textContent = `${(confidence * 100).toFixed(0)}%`;
        return badge;
    }
    
    formatMessage(text) {
        // Convert URLs to links
        const urlRegex = /(https?:\/\/[^\s]+)/g;
        text = text.replace(urlRegex, '<a href="$1" target="_blank">$1</a>');
        
        // Convert newlines to <br>
        text = text.replace(/\n/g, '<br>');
        
        return text;
    }
    
    setUIState(state) {
        switch (state) {
            case 'processing':
                this.sendButton.disabled = true;
                this.sendButton.classList.add('btn-loading');
                this.messageInput.disabled = true;
                this.typingIndicator.classList.remove('d-none');
                this.statusIndicator.textContent = 'Processing';
                this.statusIndicator.className = 'badge bg-warning';
                break;
                
            case 'ready':
                this.sendButton.disabled = false;
                this.sendButton.classList.remove('btn-loading');
                this.messageInput.disabled = false;
                this.typingIndicator.classList.add('d-none');
                this.statusIndicator.textContent = 'Online';
                this.statusIndicator.className = 'badge bg-success';
                this.messageInput.focus();
                break;
                
            case 'error':
                this.statusIndicator.textContent = 'Error';
                this.statusIndicator.className = 'badge bg-danger';
                break;
        }
    }
    
    autoResizeInput() {
        this.messageInput.style.height = 'auto';
        this.messageInput.style.height = this.messageInput.scrollHeight + 'px';
    }
    
    scrollToBottom() {
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }
    
    async loadChatHistory() {
        try {
            const response = await fetch(`${this.config.historyUrl}?limit=5`);
            if (response.ok) {
                const data = await response.json();
                // Process and display recent history if needed
                console.log('Chat history loaded:', data);
            }
        } catch (error) {
            console.error('Error loading chat history:', error);
        }
    }
}

// Global initialization function
function initializeChat(config) {
    window.chatInterface = new ChatInterface(config);
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ChatInterface, initializeChat };
}
