<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Standalone AI Widget Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        .test-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .widget-indicator {
            position: fixed;
            bottom: 100px;
            right: 30px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 14px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            animation: bounce 2s infinite;
        }
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
    </style>
    
    <!-- Load the floating widget CSS directly -->
    <link rel="stylesheet" href="ai_assistant/static/ai_assistant/css/floating-widget.css">
</head>
<body>
    <div class="test-container">
        <h1>🤖 Standalone AI Assistant Widget Test</h1>
        <p>This page tests the floating AI assistant widget without Django.</p>
        
        <div class="test-info">
            <h3>Instructions:</h3>
            <p>1. Look for the floating button in the bottom-right corner</p>
            <p>2. Click the button to open the chat widget</p>
            <p>3. Try typing a message</p>
            <p>4. Test the interface elements</p>
        </div>
        
        <div class="test-info">
            <h3>Widget Features:</h3>
            <ul style="text-align: left; max-width: 400px; margin: 0 auto;">
                <li>✅ Floating button design</li>
                <li>✅ Expandable chat interface</li>
                <li>✅ Text input with auto-resize</li>
                <li>✅ Voice recording button</li>
                <li>✅ File upload functionality</li>
                <li>✅ Quick action buttons</li>
                <li>✅ Typing indicators</li>
                <li>✅ Mobile responsive design</li>
            </ul>
        </div>
    </div>
    
    <!-- Widget Indicator -->
    <div class="widget-indicator" id="widgetIndicator">
        Look for the AI button! →
    </div>
    
    <!-- Load the floating widget JavaScript -->
    <script src="ai_assistant/static/ai_assistant/js/floating-widget.js"></script>
    
    <!-- Initialize the widget with mock API -->
    <script>
        // Mock API responses for testing
        const mockApiResponse = {
            success: true,
            message: "This is a mock response from the AI assistant. In a real implementation, this would connect to your Django backend.",
            action: "message",
            url: null,
            filters: {},
            confidence: 0.95
        };
        
        // Mock fetch function for testing
        const originalFetch = window.fetch;
        window.fetch = function(url, options) {
            console.log('Mock API call to:', url, options);
            
            // Simulate API delay
            return new Promise(resolve => {
                setTimeout(() => {
                    resolve({
                        ok: true,
                        json: () => Promise.resolve(mockApiResponse)
                    });
                }, 1000);
            });
        };
        
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing widget...');
            
            // Check if FloatingAIWidget class is available
            if (typeof FloatingAIWidget !== 'undefined') {
                try {
                    window.aiWidget = new FloatingAIWidget({
                        apiUrl: '/mock-api/message/',
                        uploadUrl: '/mock-api/upload/',
                        enableVoice: true,
                        enableFileUpload: true,
                        maxFileSize: 10 * 1024 * 1024,
                        allowedFileTypes: ['image/*', 'application/pdf', '.doc', '.docx', '.txt', '.csv'],
                        theme: 'light',
                        position: 'bottom-right'
                    });
                    
                    console.log('AI Widget initialized successfully');
                    
                    // Hide indicator after widget is opened
                    const indicator = document.getElementById('widgetIndicator');
                    const checkWidget = setInterval(() => {
                        if (window.aiWidget && window.aiWidget.isOpen) {
                            indicator.style.display = 'none';
                            clearInterval(checkWidget);
                        }
                    }, 1000);
                    
                    // Auto-hide indicator after 10 seconds
                    setTimeout(() => {
                        indicator.style.opacity = '0';
                        setTimeout(() => {
                            indicator.style.display = 'none';
                        }, 500);
                    }, 10000);
                    
                } catch (error) {
                    console.error('Widget initialization error:', error);
                    alert('Widget initialization failed: ' + error.message);
                }
            } else {
                console.error('FloatingAIWidget class not found');
                alert('FloatingAIWidget class not found. Please check if the JavaScript file loaded correctly.');
            }
        });
        
        // Additional debugging
        window.addEventListener('load', function() {
            console.log('Page fully loaded');
            console.log('FloatingAIWidget available:', typeof FloatingAIWidget !== 'undefined');
            
            // Check if CSS loaded
            const cssLoaded = document.querySelector('link[href*="floating-widget.css"]');
            console.log('CSS loaded:', !!cssLoaded);
            
            // Check if JS loaded
            const jsLoaded = document.querySelector('script[src*="floating-widget.js"]');
            console.log('JS loaded:', !!jsLoaded);
        });
        
        // Test function to manually trigger widget
        function testWidget() {
            if (window.aiWidget) {
                window.aiWidget.openWidget();
                setTimeout(() => {
                    window.aiWidget.textInput.value = "Hello! This is a test message.";
                    window.aiWidget.sendMessage();
                }, 500);
            }
        }
        
        // Add test button
        document.addEventListener('DOMContentLoaded', function() {
            const testButton = document.createElement('button');
            testButton.textContent = 'Test Widget';
            testButton.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 10px 20px;
                background: #667eea;
                color: white;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                z-index: 10000;
            `;
            testButton.onclick = testWidget;
            document.body.appendChild(testButton);
        });
    </script>
</body>
</html>
