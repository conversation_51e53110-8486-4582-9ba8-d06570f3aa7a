# Leave Allocation Create API

## 📋 Overview

Create leave allocation for employee in the Eaglora HRMS system.

## 🔗 Endpoint Details

- **URL:** `/leave-allocation/`
- **Method:** `POST`
- **Authentication:** Required (JWT Token)
- **Permissions:** `leave.add_leave`
- **Content-Type:** `application/json`

## 🔐 Authentication

```http
Authorization: Bearer YOUR_JWT_TOKEN
```

## 📥 Request

### Example Request
```bash
curl -X POST "http://localhost:8000/api/leave/leave-allocation/" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

## 📤 Response

### Success Response
```json
{
  "id": 1,
  "status": "success",
  "message": "Operation completed successfully",
  "data": {}
}
```

## 🔧 Usage Examples

### Python Example
```python
import requests

url = "http://localhost:8000/api/leave/leave-allocation/"
headers = {
    "Authorization": "Bearer YOUR_JWT_TOKEN",
    "Content-Type": "application/json"
}

response = requests.post(url, headers=headers)
if response.status_code == 200:
    data = response.json()
    print("Success:", data)
else:
    print("Error:", response.status_code)
```

### JavaScript Example
```javascript
const url = 'http://localhost:8000/api/leave/leave-allocation/';

fetch(url, {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer YOUR_JWT_TOKEN',
        'Content-Type': 'application/json'
    }
})
.then(response => response.json())
.then(data => console.log('Success:', data))
.catch(error => console.error('Error:', error));
```

## 📊 Use Cases

This API is commonly used for:
- **Annual setup**
- **New employee**
- **Policy changes**

## 🚨 Error Responses

### Unauthorized (401)
```json
{
  "detail": "Authentication credentials were not provided."
}
```

### Forbidden (403)
```json
{
  "detail": "You do not have permission to perform this action."
}
```

## 🔄 Related APIs

- [Leave Module](README.md)
- [API Documentation Home](../README.md)

---

**Back to:** [Leave Module](README.md)
