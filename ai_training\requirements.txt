# Eaglora HRMS AI Training Requirements
# Core ML/AI Libraries
scikit-learn>=1.3.0
pandas>=2.0.0
numpy>=1.24.0
joblib>=1.3.0

# HTTP and API Libraries
requests>=2.31.0

# Data Processing
scipy>=1.10.0

# Optional: Advanced ML Libraries (for future enhancements)
# tensorflow>=2.13.0
# torch>=2.0.0
# transformers>=4.30.0

# Development and Testing
pytest>=7.4.0
jupyter>=1.0.0

# Utilities
tqdm>=4.65.0
python-dateutil>=2.8.0
