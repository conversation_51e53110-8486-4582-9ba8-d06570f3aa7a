# Eaglora HRMS - Complete API Documentation

## 📋 Overview

This directory contains comprehensive API documentation for all modules in the Eaglora HRMS system. Each API endpoint is documented in detail with separate files for easy navigation and maintenance.

## 🏗️ API Structure

The Eaglora HRMS API is organized into the following modules:

### 🔐 Authentication Module
**Base URL:** `/api/auth/`
- User authentication and JWT token management
- Login, logout, and token refresh functionality

### 👥 Employee Module  
**Base URL:** `/api/employee/`
- Employee CRUD operations
- Employee work information management
- Bank details and personal information
- Search, filtering, and bulk operations

### ⏰ Attendance Module
**Base URL:** `/api/attendance/`
- Clock in/out functionality
- Attendance tracking and validation
- Overtime management
- Attendance requests and approvals

### 🏖️ Leave Module
**Base URL:** `/api/leave/`
- Leave request management
- Leave type configuration
- Leave allocation and approval
- Holiday management

### 💰 Payroll Module
**Base URL:** `/api/payroll/`
- Payslip generation and management
- Contract management
- Allowances and deductions
- Tax bracket configuration
- Loan and reimbursement management

### 🏢 Asset Module
**Base URL:** `/api/asset/`
- Asset management and tracking
- Asset allocation and requests
- Asset categories and lots
- Asset approval workflow

### 🏗️ Base Module
**Base URL:** `/api/base/`
- Company management
- Department and job position management
- Job roles and organizational structure
- Core configuration settings

### 🔔 Notifications Module
**Base URL:** `/api/notifications/`
- Notification management
- Read/unread status tracking
- Bulk notification operations

### 🤖 AI Assistant Module
**Base URL:** `/ai-assistant/api/`
- AI-powered chat interface
- HRMS integration endpoints
- Employee search and quick actions

## 📁 Documentation Structure

```
docs/api-documentation/
├── README.md                    # This file
├── auth/                        # Authentication APIs
│   ├── login.md
│   └── README.md
├── employee/                    # Employee Management APIs
│   ├── employees-list.md
│   ├── employee-detail.md
│   ├── employee-create.md
│   ├── employee-update.md
│   ├── employee-delete.md
│   ├── employee-work-info.md
│   ├── employee-bank-details.md
│   ├── employee-types.md
│   ├── employee-bulk-operations.md
│   └── README.md
├── attendance/                  # Attendance Management APIs
│   ├── clock-in.md
│   ├── clock-out.md
│   ├── attendance-list.md
│   ├── attendance-requests.md
│   ├── overtime-management.md
│   └── README.md
├── leave/                       # Leave Management APIs
│   ├── leave-requests.md
│   ├── leave-types.md
│   ├── leave-allocation.md
│   ├── leave-approval.md
│   ├── holidays.md
│   └── README.md
├── payroll/                     # Payroll Management APIs
│   ├── contracts.md
│   ├── payslips.md
│   ├── allowances.md
│   ├── deductions.md
│   ├── tax-brackets.md
│   ├── loans.md
│   ├── reimbursements.md
│   └── README.md
├── asset/                       # Asset Management APIs
│   ├── assets.md
│   ├── asset-categories.md
│   ├── asset-lots.md
│   ├── asset-allocations.md
│   ├── asset-requests.md
│   └── README.md
├── base/                        # Base Configuration APIs
│   ├── companies.md
│   ├── departments.md
│   ├── job-positions.md
│   ├── job-roles.md
│   └── README.md
├── notifications/               # Notification APIs
│   ├── notifications-list.md
│   ├── notification-actions.md
│   └── README.md
└── ai-assistant/               # AI Assistant APIs
    ├── chat-message.md
    ├── employee-search.md
    ├── quick-actions.md
    └── README.md
```

## 🚀 Quick Start

### Prerequisites
- Eaglora HRMS system running
- Valid user credentials
- API access permissions

### Authentication
All API endpoints require authentication using JWT tokens:

1. **Get JWT Token:**
   ```bash
   curl -X POST "http://localhost:8000/api/auth/login/" \
     -H "Content-Type: application/json" \
     -d '{"username": "your_username", "password": "your_password"}'
   ```

2. **Use Token in Requests:**
   ```bash
   curl -X GET "http://localhost:8000/api/endpoint/" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -H "Content-Type: application/json"
   ```

### Base URL
```
Production: https://your-domain.com/api/
Development: http://localhost:8000/api/
```

## 📊 API Statistics

| Module | Endpoints | CRUD Operations | Special Features |
|--------|-----------|-----------------|------------------|
| Authentication | 1 | Login | JWT Token Management |
| Employee | 15+ | Full CRUD | Search, Filter, Bulk Ops |
| Attendance | 12+ | Full CRUD | Clock In/Out, Validation |
| Leave | 15+ | Full CRUD | Approval Workflow |
| Payroll | 8+ | Full CRUD | PDF Generation |
| Asset | 8+ | Full CRUD | Allocation Workflow |
| Base | 8+ | Full CRUD | Organizational Structure |
| Notifications | 4+ | Read/Update | Bulk Operations |
| AI Assistant | 5+ | Custom | Natural Language |

## 🔧 Common Features

### Pagination
Most list endpoints support pagination:
```json
{
  "count": 150,
  "next": "http://api.example.com/endpoint/?page=2",
  "previous": null,
  "results": [...]
}
```

### Filtering
Many endpoints support filtering via query parameters:
```
?field_name=value&another_field=value
```

### Search
Search functionality available on relevant endpoints:
```
?search=search_term
```

### Ordering
Results can be ordered using:
```
?ordering=field_name
?ordering=-field_name  # Descending
```

## 🛡️ Security Features

- **JWT Authentication** - Secure token-based authentication
- **Permission-based Access** - Role-based access control
- **Input Validation** - Comprehensive data validation
- **Rate Limiting** - API abuse prevention
- **HTTPS Support** - Secure communication

## 📝 Response Formats

### Success Response
```json
{
  "id": 1,
  "field1": "value1",
  "field2": "value2",
  "created_at": "2024-01-01T10:00:00Z"
}
```

### Error Response
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field_name": ["Error message"]
    }
  }
}
```

### List Response
```json
{
  "count": 100,
  "next": "http://api.example.com/endpoint/?page=2",
  "previous": null,
  "results": [...]
}
```

## 🔍 How to Use This Documentation

1. **Browse by Module** - Navigate to the specific module folder
2. **Find Your Endpoint** - Each endpoint has its own detailed file
3. **Check Examples** - Every endpoint includes request/response examples
4. **Test the API** - Use the provided curl examples
5. **Understand Permissions** - Check required permissions for each endpoint

## 📞 Support

For questions or issues:
1. Check the specific endpoint documentation
2. Review the error response format
3. Verify authentication and permissions
4. Contact the development team

## 📅 Version Information

- **API Version:** 1.0.0
- **Documentation Version:** 1.0.0
- **Last Updated:** January 2024
- **Compatibility:** Eaglora HRMS v2.0+

---

**Navigate to specific modules using the links above or browse the folder structure directly.**
