#!/usr/bin/env python3
"""
Debug script to check floating widget integration
"""

import os
import sys

def check_widget_integration():
    """Check if the floating widget is properly integrated"""
    print("🔍 Debugging Floating AI Widget Integration")
    print("=" * 50)
    
    # Check if files exist
    files_to_check = [
        'ai_assistant/static/ai_assistant/css/floating-widget.css',
        'ai_assistant/static/ai_assistant/js/floating-widget.js',
        'ai_assistant/templates/ai_assistant/floating_widget_simple.html',
        'templates/index.html'
    ]
    
    print("\n📁 File Existence Check:")
    for file_path in files_to_check:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"✅ {file_path} ({size} bytes)")
        else:
            print(f"❌ {file_path} - NOT FOUND")
    
    # Check if widget is included in index.html
    print("\n📄 Template Integration Check:")
    try:
        with open('templates/index.html', 'r', encoding='utf-8') as f:
            content = f.read()
            
        if 'floating_widget_simple.html' in content:
            print("✅ Widget template is included in index.html")
        elif 'floating_widget.html' in content:
            print("✅ Widget template is included in index.html (original version)")
        else:
            print("❌ Widget template is NOT included in index.html")
            
        # Check for include statement
        if '{% include' in content and 'ai_assistant' in content:
            print("✅ Include statement found")
        else:
            print("❌ Include statement not found")
            
    except Exception as e:
        print(f"❌ Error reading index.html: {e}")
    
    # Check Django settings
    print("\n⚙️ Django Settings Check:")
    try:
        with open('eaglora/settings.py', 'r', encoding='utf-8') as f:
            settings_content = f.read()
            
        # Check INSTALLED_APPS
        if '"ai_assistant"' in settings_content:
            print("✅ ai_assistant app is in INSTALLED_APPS")
        else:
            print("❌ ai_assistant app is NOT in INSTALLED_APPS")
            
        # Check static files configuration
        if 'STATIC_URL' in settings_content:
            print("✅ Static files configuration found")
        else:
            print("❌ Static files configuration not found")
            
    except Exception as e:
        print(f"❌ Error reading settings.py: {e}")
    
    # Check URLs
    print("\n🔗 URL Configuration Check:")
    try:
        with open('eaglora/urls.py', 'r', encoding='utf-8') as f:
            urls_content = f.read()
            
        if 'ai-assistant' in urls_content:
            print("✅ ai-assistant URLs are included")
        else:
            print("❌ ai-assistant URLs are NOT included")
            
    except Exception as e:
        print(f"❌ Error reading urls.py: {e}")
    
    # Check static files in staticfiles directory
    print("\n📦 Static Files Check:")
    staticfiles_css = 'staticfiles/ai_assistant/css/floating-widget.css'
    staticfiles_js = 'staticfiles/ai_assistant/js/floating-widget.js'
    
    if os.path.exists(staticfiles_css):
        print(f"✅ CSS file in staticfiles: {staticfiles_css}")
    else:
        print(f"❌ CSS file NOT in staticfiles: {staticfiles_css}")
        
    if os.path.exists(staticfiles_js):
        print(f"✅ JS file in staticfiles: {staticfiles_js}")
    else:
        print(f"❌ JS file NOT in staticfiles: {staticfiles_js}")
    
    # Provide troubleshooting steps
    print("\n🛠️ Troubleshooting Steps:")
    print("1. Run: python manage.py collectstatic --noinput")
    print("2. Start Django server: python manage.py runserver")
    print("3. Visit: http://localhost:8000/")
    print("4. Check browser console for errors (F12)")
    print("5. Look for floating button in bottom-right corner")
    
    # Check if we can provide a direct link
    print("\n🔗 Test URLs:")
    print("- Main dashboard: http://localhost:8000/")
    print("- Widget test page: http://localhost:8000/ai-assistant/test/widget/")
    print("- Widget demo page: http://localhost:8000/ai-assistant/demo/widget/")
    
    print("\n💡 Quick Fix:")
    print("If the widget is not visible, try adding this to any template:")
    print('{% include "ai_assistant/floating_widget_simple.html" %}')

def create_minimal_test():
    """Create a minimal test file"""
    print("\n🧪 Creating minimal test file...")
    
    minimal_html = '''<!DOCTYPE html>
<html>
<head>
    <title>Minimal Widget Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f0f0f0; }
        .test-button { 
            background: #667eea; 
            color: white; 
            padding: 15px 30px; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
            font-size: 16px;
            margin: 10px;
        }
        .test-button:hover { background: #5a6fd8; }
    </style>
</head>
<body>
    <h1>🤖 Minimal AI Widget Test</h1>
    <p>This is a minimal test to check if the widget files exist and can be loaded.</p>
    
    <button class="test-button" onclick="checkFiles()">Check Widget Files</button>
    <button class="test-button" onclick="loadWidget()">Load Widget</button>
    
    <div id="status"></div>
    
    <script>
        function checkFiles() {
            const status = document.getElementById('status');
            status.innerHTML = '<h3>Checking files...</h3>';
            
            // Try to load CSS
            const css = document.createElement('link');
            css.rel = 'stylesheet';
            css.href = '/static/ai_assistant/css/floating-widget.css';
            css.onload = () => status.innerHTML += '<p>✅ CSS loaded successfully</p>';
            css.onerror = () => status.innerHTML += '<p>❌ CSS failed to load</p>';
            document.head.appendChild(css);
            
            // Try to load JS
            const js = document.createElement('script');
            js.src = '/static/ai_assistant/js/floating-widget.js';
            js.onload = () => {
                status.innerHTML += '<p>✅ JavaScript loaded successfully</p>';
                status.innerHTML += '<p>FloatingAIWidget available: ' + (typeof FloatingAIWidget !== 'undefined') + '</p>';
            };
            js.onerror = () => status.innerHTML += '<p>❌ JavaScript failed to load</p>';
            document.head.appendChild(js);
        }
        
        function loadWidget() {
            if (typeof FloatingAIWidget !== 'undefined') {
                try {
                    window.testWidget = new FloatingAIWidget({
                        apiUrl: '/ai-assistant/api/message/',
                        uploadUrl: '/ai-assistant/api/upload/',
                        enableVoice: true,
                        enableFileUpload: true
                    });
                    document.getElementById('status').innerHTML += '<p>✅ Widget initialized successfully!</p>';
                } catch (error) {
                    document.getElementById('status').innerHTML += '<p>❌ Widget initialization failed: ' + error.message + '</p>';
                }
            } else {
                document.getElementById('status').innerHTML += '<p>❌ FloatingAIWidget class not available</p>';
            }
        }
    </script>
</body>
</html>'''
    
    with open('minimal_widget_test.html', 'w', encoding='utf-8') as f:
        f.write(minimal_html)
    
    print("✅ Created minimal_widget_test.html")
    print("📂 Open this file in your browser to test widget loading")

if __name__ == "__main__":
    check_widget_integration()
    create_minimal_test()
    
    print("\n" + "=" * 50)
    print("🎯 Summary:")
    print("1. Check the output above for any ❌ errors")
    print("2. Run 'python manage.py collectstatic' if static files are missing")
    print("3. Start Django server and visit http://localhost:8000/")
    print("4. Look for floating button in bottom-right corner")
    print("5. If still not visible, check browser console (F12) for errors")
    print("\n💬 The widget should appear as a floating button with a robot icon!")
