version: "3.1"

stories:

- story: happy path
  steps:
  - intent: greet
  - action: utter_greet
  - intent: get_employee_profile
  - action: action_get_employee_profile
  - intent: goodbye
  - action: utter_goodbye

- story: say goodbye
  steps:
  - intent: goodbye
  - action: utter_goodbye

- story: bot challenge
  steps:
  - intent: bot_challenge
  - action: utter_iamabot

- story: attendance flow
  steps:
  - intent: greet
  - action: utter_greet
  - intent: clock_in
  - action: action_clock_in
  - intent: goodbye
  - action: utter_goodbye

- story: leave application flow
  steps:
  - intent: greet
  - action: utter_greet
  - intent: apply_leave
  - action: action_apply_leave
  - intent: goodbye
  - action: utter_goodbye

- story: check leave balance
  steps:
  - intent: greet
  - action: utter_greet
  - intent: check_leave_balance
  - action: action_check_leave_balance
  - intent: goodbye
  - action: utter_goodbye
