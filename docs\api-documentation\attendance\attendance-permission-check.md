# Attendance Permission Check API

## 📋 Overview

Check attendance permissions for user in the Eaglora HRMS system.

## 🔗 Endpoint Details

- **URL:** `/permission-check/attendance`
- **Method:** `GET`
- **Authentication:** Required (JWT <PERSON>ken)
- **Permissions:** `attendance.view_attendance`
- **Content-Type:** `application/json`

## 🔐 Authentication

```http
Authorization: Bearer YOUR_JWT_TOKEN
```

## 📥 Request

### Example Request
```bash
curl -X GET "http://localhost:8000/api/attendance/permission-check/attendance" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

## 📤 Response

### Success Response
```json
{
  "id": 1,
  "status": "success",
  "message": "Operation completed successfully",
  "data": {}
}
```

## 🔧 Usage Examples

### Python Example
```python
import requests

url = "http://localhost:8000/api/attendance/permission-check/attendance"
headers = {
    "Authorization": "Bearer YOUR_JWT_TOKEN",
    "Content-Type": "application/json"
}

response = requests.get(url, headers=headers)
if response.status_code == 200:
    data = response.json()
    print("Success:", data)
else:
    print("Error:", response.status_code)
```

### JavaScript Example
```javascript
const url = 'http://localhost:8000/api/attendance/permission-check/attendance';

fetch(url, {
    method: 'GET',
    headers: {
        'Authorization': 'Bearer YOUR_JWT_TOKEN',
        'Content-Type': 'application/json'
    }
})
.then(response => response.json())
.then(data => console.log('Success:', data))
.catch(error => console.error('Error:', error));
```

## 📊 Use Cases

This API is commonly used for:
- **Permission validation**
- **Access control**
- **UI customization**

## 🚨 Error Responses

### Unauthorized (401)
```json
{
  "detail": "Authentication credentials were not provided."
}
```

### Forbidden (403)
```json
{
  "detail": "You do not have permission to perform this action."
}
```

## 🔄 Related APIs

- [Attendance Module](README.md)
- [API Documentation Home](../README.md)

---

**Back to:** [Attendance Module](README.md)
