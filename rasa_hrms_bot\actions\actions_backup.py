"""
Custom Actions for Eaglora HRMS Rasa Bot

This module contains custom actions that integrate with the Eaglora HRMS API
to provide real-time HR functionality through the conversational interface.
"""

import requests
import json
from typing import Any, Text, Dict, List
from datetime import datetime, date
import logging

from rasa_sdk import Action, Tracker
from rasa_sdk.executor import CollectingDispatcher
from rasa_sdk.events import SlotSet, FollowupAction

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# HRMS API Configuration
HRMS_BASE_URL = "http://localhost:8000"  # Update with your HRMS URL
API_ENDPOINTS = {
    "login": "/api/auth/login/",
    "employee_profile": "/api/employee/employees/",
    "employee_search": "/api/employee/employees/",
    "attendance": "/api/attendance/attendance/",
    "clock_in": "/api/attendance/clock-in/",
    "clock_out": "/api/attendance/clock-out/",
    "leave_requests": "/api/leave/user-request/",
    "leave_balance": "/api/leave/available-leave/",
    "leave_approve": "/api/leave/approve/",
    "leave_reject": "/api/leave/reject/",
    "payroll": "/api/payroll/payslip/",
    "assets": "/api/asset/assets/",
    "asset_requests": "/api/asset/asset-requests/",
    "notifications": "/api/notifications/",
    "departments": "/api/base/departments/",
    "companies": "/api/base/companies/",
    "holidays": "/api/base/holidays/",
}

class HRMSAPIClient:
    """Client for interacting with HRMS API"""

    def __init__(self, base_url: str):
        self.base_url = base_url
        self.session = requests.Session()
        self.token = None

    def authenticate(self, username: str, password: str) -> bool:
        """Authenticate with HRMS API"""
        try:
            response = self.session.post(
                f"{self.base_url}{API_ENDPOINTS['login']}",
                json={"username": username, "password": password}
            )
            if response.status_code == 200:
                data = response.json()
                self.token = data.get("access")
                self.session.headers.update({
                    "Authorization": f"Bearer {self.token}"
                })
                return True
            return False
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            return False

    def get(self, endpoint: str, params: dict = None) -> dict:
        """Make GET request to HRMS API"""
        try:
            response = self.session.get(
                f"{self.base_url}{endpoint}",
                params=params
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"GET request error: {e}")
            return {"error": str(e)}

    def post(self, endpoint: str, data: dict = None) -> dict:
        """Make POST request to HRMS API"""
        try:
            response = self.session.post(
                f"{self.base_url}{endpoint}",
                json=data
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"POST request error: {e}")
            return {"error": str(e)}

# Initialize API client
api_client = HRMSAPIClient(HRMS_BASE_URL)


class ActionGetEmployeeProfile(Action):
    """Action to get employee profile information"""

    def name(self) -> Text:
        return "action_get_employee_profile"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:

        employee_name = tracker.get_slot("employee_name")
        employee_id = tracker.get_slot("employee_id")
        
        if not employee_name and not employee_id:
            dispatcher.utter_message(text="Please provide an employee name or ID.")
            return []

        try:
            # Call Django API
            params = {}
            if employee_name:
                params['search'] = employee_name
            if employee_id:
                params['employee_id'] = employee_id

            response = requests.get(f"{DJANGO_API_BASE}/employee/employees/", params=params)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('results'):
                    employee = data['results'][0]
                    message = f"Here's the profile for {employee.get('employee_first_name', '')} {employee.get('employee_last_name', '')}:"
                    
                    dispatcher.utter_message(
                        text=message,
                        json_message={
                            "action": "show_profile",
                            "url": f"/employee/view/{employee.get('id')}/",
                            "filters": {"employee_id": employee.get('id')},
                            "message": message,
                            "data": employee
                        }
                    )
                else:
                    dispatcher.utter_message(text="Employee not found.")
            else:
                dispatcher.utter_message(text="Sorry, I couldn't retrieve the employee information.")

        except Exception as e:
            logger.error(f"Error in ActionGetEmployeeProfile: {str(e)}")
            dispatcher.utter_message(text="Sorry, there was an error retrieving the employee information.")

        return []


class ActionSearchEmployee(Action):
    """Action to search for employees"""

    def name(self) -> Text:
        return "action_search_employee"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:

        employee_name = tracker.get_slot("employee_name")
        department = tracker.get_slot("department")
        
        if not employee_name and not department:
            dispatcher.utter_message(text="Please provide a name or department to search for.")
            return []

        try:
            params = {}
            if employee_name:
                params['search'] = employee_name
            if department:
                params['department'] = department

            response = requests.get(f"{DJANGO_API_BASE}/employee/employees/", params=params)
            
            if response.status_code == 200:
                data = response.json()
                employees = data.get('results', [])
                
                if employees:
                    if len(employees) == 1:
                        employee = employees[0]
                        message = f"Found: {employee.get('employee_first_name', '')} {employee.get('employee_last_name', '')}"
                    else:
                        message = f"Found {len(employees)} employees matching your search."
                    
                    dispatcher.utter_message(
                        text=message,
                        json_message={
                            "action": "show_search_results",
                            "url": "/employee/",
                            "filters": params,
                            "message": message,
                            "data": employees
                        }
                    )
                else:
                    dispatcher.utter_message(text="No employees found matching your search.")
            else:
                dispatcher.utter_message(text="Sorry, I couldn't search for employees.")

        except Exception as e:
            logger.error(f"Error in ActionSearchEmployee: {str(e)}")
            dispatcher.utter_message(text="Sorry, there was an error searching for employees.")

        return []


class ActionApplyLeave(Action):
    """Action to apply for leave"""

    def name(self) -> Text:
        return "action_apply_leave"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:

        leave_type = tracker.get_slot("leave_type")
        start_date = tracker.get_slot("start_date")
        end_date = tracker.get_slot("end_date")
        reason = tracker.get_slot("reason")
        
        if not all([leave_type, start_date, end_date]):
            missing = []
            if not leave_type:
                missing.append("leave type")
            if not start_date:
                missing.append("start date")
            if not end_date:
                missing.append("end date")
            
            dispatcher.utter_message(text=f"Please provide the {', '.join(missing)} for your leave application.")
            return []

        try:
            # Prepare leave application data
            leave_data = {
                "leave_type": leave_type,
                "start_date": start_date,
                "end_date": end_date,
                "reason": reason or "Applied via AI Assistant"
            }

            # Note: This would need proper authentication in real implementation
            response = requests.post(f"{DJANGO_API_BASE}/leave/user-request/", json=leave_data)
            
            if response.status_code == 201:
                message = f"Your {leave_type} application from {start_date} to {end_date} has been submitted successfully!"
                
                dispatcher.utter_message(
                    text=message,
                    json_message={
                        "action": "leave_applied",
                        "url": "/leave/request/",
                        "filters": {"status": "pending"},
                        "message": message,
                        "data": leave_data
                    }
                )
            else:
                dispatcher.utter_message(text="Sorry, I couldn't submit your leave application. Please try again or contact HR.")

        except Exception as e:
            logger.error(f"Error in ActionApplyLeave: {str(e)}")
            dispatcher.utter_message(text="Sorry, there was an error submitting your leave application.")

        return []


class ActionCheckLeaveBalance(Action):
    """Action to check leave balance"""

    def name(self) -> Text:
        return "action_check_leave_balance"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:

        try:
            # Note: This would need proper user authentication in real implementation
            response = requests.get(f"{DJANGO_API_BASE}/leave/available-leave/")
            
            if response.status_code == 200:
                data = response.json()
                leave_balances = data.get('results', [])
                
                if leave_balances:
                    message = "Here's your current leave balance:\n"
                    for balance in leave_balances:
                        leave_type = balance.get('leave_type_id', {}).get('name', 'Unknown')
                        available = balance.get('available_days', 0)
                        message += f"• {leave_type}: {available} days\n"
                    
                    dispatcher.utter_message(
                        text=message,
                        json_message={
                            "action": "show_leave_balance",
                            "url": "/leave/available-leave/",
                            "filters": {},
                            "message": message,
                            "data": leave_balances
                        }
                    )
                else:
                    dispatcher.utter_message(text="No leave balance information found.")
            else:
                dispatcher.utter_message(text="Sorry, I couldn't retrieve your leave balance.")

        except Exception as e:
            logger.error(f"Error in ActionCheckLeaveBalance: {str(e)}")
            dispatcher.utter_message(text="Sorry, there was an error retrieving your leave balance.")

        return []


class ActionCheckAttendance(Action):
    """Action to check attendance"""

    def name(self) -> Text:
        return "action_check_attendance"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:

        date_range = tracker.get_slot("date_range")
        employee_name = tracker.get_slot("employee_name")

        try:
            params = {}
            if date_range:
                params['date_range'] = date_range
            if employee_name:
                params['employee_name'] = employee_name

            response = requests.get(f"{DJANGO_API_BASE}/attendance/attendance/", params=params)
            
            if response.status_code == 200:
                data = response.json()
                attendance_records = data.get('results', [])
                
                if attendance_records:
                    message = f"Here's the attendance information"
                    if date_range:
                        message += f" for {date_range}"
                    if employee_name:
                        message += f" for {employee_name}"
                    message += ":"
                    
                    dispatcher.utter_message(
                        text=message,
                        json_message={
                            "action": "show_attendance",
                            "url": "/attendance/view/",
                            "filters": params,
                            "message": message,
                            "data": attendance_records
                        }
                    )
                else:
                    dispatcher.utter_message(text="No attendance records found for the specified criteria.")
            else:
                dispatcher.utter_message(text="Sorry, I couldn't retrieve attendance information.")

        except Exception as e:
            logger.error(f"Error in ActionCheckAttendance: {str(e)}")
            dispatcher.utter_message(text="Sorry, there was an error retrieving attendance information.")

        return []


class ActionClockIn(Action):
    """Action to clock in"""

    def name(self) -> Text:
        return "action_clock_in"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:

        try:
            response = requests.post(f"{DJANGO_API_BASE}/attendance/clock-in/")
            
            if response.status_code == 200:
                message = "You have successfully clocked in! Have a productive day!"
                
                dispatcher.utter_message(
                    text=message,
                    json_message={
                        "action": "clock_in_success",
                        "url": "/attendance/",
                        "filters": {},
                        "message": message,
                        "data": {"action": "clock_in", "timestamp": "now"}
                    }
                )
            else:
                dispatcher.utter_message(text="Sorry, I couldn't clock you in. Please try again or use the manual system.")

        except Exception as e:
            logger.error(f"Error in ActionClockIn: {str(e)}")
            dispatcher.utter_message(text="Sorry, there was an error clocking you in.")

        return []


class ActionClockOut(Action):
    """Action to clock out"""

    def name(self) -> Text:
        return "action_clock_out"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:

        try:
            response = requests.post(f"{DJANGO_API_BASE}/attendance/clock-out/")
            
            if response.status_code == 200:
                message = "You have successfully clocked out! Have a great rest of your day!"
                
                dispatcher.utter_message(
                    text=message,
                    json_message={
                        "action": "clock_out_success",
                        "url": "/attendance/",
                        "filters": {},
                        "message": message,
                        "data": {"action": "clock_out", "timestamp": "now"}
                    }
                )
            else:
                dispatcher.utter_message(text="Sorry, I couldn't clock you out. Please try again or use the manual system.")

        except Exception as e:
            logger.error(f"Error in ActionClockOut: {str(e)}")
            dispatcher.utter_message(text="Sorry, there was an error clocking you out.")

        return []


class ActionDefaultFallback(Action):
    """Default fallback action when confidence is low"""

    def name(self) -> Text:
        return "action_default_fallback"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:

        message = ("I'm sorry, I didn't understand that. I can help you with:\n"
                  "• Employee profiles and information\n"
                  "• Leave applications and balance\n"
                  "• Attendance tracking\n"
                  "• Payroll information\n"
                  "• HR policies\n\n"
                  "Could you please rephrase your question?")
        
        dispatcher.utter_message(text=message)
        return []
