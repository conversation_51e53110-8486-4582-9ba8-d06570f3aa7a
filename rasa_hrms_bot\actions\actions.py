"""
Simple Actions for Eaglora HRMS Rasa Bot
"""

from typing import Any, Text, Dict, List
from rasa_sdk import Action, Tracker
from rasa_sdk.executor import CollectingDispatcher


class ActionGetEmployeeProfile(Action):
    """Action to get employee profile information"""

    def name(self) -> Text:
        return "action_get_employee_profile"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        
        dispatcher.utter_message(text="I can help you view your employee profile. This feature will show your personal information, job details, and contact information.")
        return []


class ActionClockIn(Action):
    """Action to clock in"""

    def name(self) -> Text:
        return "action_clock_in"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        
        dispatcher.utter_message(text="Clock-in feature activated! You have been marked as present for today.")
        return []


class ActionClockOut(Action):
    """Action to clock out"""

    def name(self) -> Text:
        return "action_clock_out"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        
        dispatcher.utter_message(text="Clock-out successful! Your work hours have been recorded.")
        return []


class ActionCheckAttendance(Action):
    """Action to check attendance"""

    def name(self) -> Text:
        return "action_check_attendance"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        
        dispatcher.utter_message(text="Your attendance record shows you have been present for 22 days this month with 95% attendance rate.")
        return []


class ActionApplyLeave(Action):
    """Action to apply for leave"""

    def name(self) -> Text:
        return "action_apply_leave"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        
        dispatcher.utter_message(text="Leave application process initiated. Please specify the leave type, start date, and end date for your leave request.")
        return []


class ActionCheckLeaveBalance(Action):
    """Action to check leave balance"""

    def name(self) -> Text:
        return "action_check_leave_balance"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:
        
        dispatcher.utter_message(text="Your current leave balance: Annual Leave: 15 days, Sick Leave: 8 days, Personal Leave: 5 days.")
        return []
