# Eaglora HRMS - Employee API Quick Reference

## Base URL
```
https://your-domain.com/api/employee/
```

## Authentication
```http
Authorization: Bearer <token>
Content-Type: application/json
```

## Quick Endpoints Reference

### Core Employee Operations
| Method | Endpoint | Description | Permissions |
|--------|----------|-------------|-------------|
| GET | `/employees/` | List all employees | `employee.view_employee` |
| GET | `/employees/{id}/` | Get employee details | `employee.view_employee` |
| POST | `/employees/` | Create employee | `employee.add_employee` |
| PUT | `/employees/{id}/` | Update employee | `employee.change_employee` |
| DELETE | `/employees/{id}/` | Delete employee | `employee.delete_employee` |

### Employee Work Information
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/employee-work-information/` | List work info |
| POST | `/employee-work-information/` | Create work info |
| PUT | `/employee-work-information/{id}/` | Update work info |

### Employee Bank Details
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/employee-bank-details/{id}/` | Get bank details |
| PUT | `/employee-bank-details/{id}/` | Update bank details |

### Search & Filtering
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/list/employees/` | Advanced search (paginated) |
| GET | `/employee-selector/` | Employee selector (hierarchy-based) |

### Bulk Operations
| Method | Endpoint | Description |
|--------|----------|-------------|
| PUT | `/employee-bulk-update/` | Bulk update employees |
| GET | `/employee-work-info-export/` | Export work info |
| POST | `/employee-work-info-import/` | Import work info |

### Additional Features
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/employee-type/` | List employee types |
| GET | `/disciplinary-action/` | List disciplinary actions |
| GET | `/documents/` | List employee documents |
| GET | `/policies/` | List company policies |

## Common Query Parameters

### Filtering Parameters
```
?employee_first_name=John
?employee_last_name=Doe
?email=<EMAIL>
?badge_id=EMP001
?phone=**********
?country=USA
?gender=male
?is_active=true
?department=IT
?search=john
```

### Pagination Parameters
```
?page=2
?page_size=20
```

### Grouping Parameters
```
?groupby_field=department
?groupby_field=employee_work_info__job_position_id
```

## Sample Requests

### 1. Get All Active Employees
```bash
curl -X GET "https://api.example.com/api/employee/employees/?is_active=true" \
  -H "Authorization: Bearer your-token"
```

### 2. Search Employees by Name
```bash
curl -X GET "https://api.example.com/api/employee/employees/?search=john" \
  -H "Authorization: Bearer your-token"
```

### 3. Create New Employee
```bash
curl -X POST "https://api.example.com/api/employee/employees/" \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "employee_first_name": "John",
    "employee_last_name": "Doe",
    "email": "<EMAIL>",
    "phone": "+**********"
  }'
```

### 4. Update Employee
```bash
curl -X PUT "https://api.example.com/api/employee/employees/1/" \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "+1987654321",
    "address": "New Address"
  }'
```

### 5. Filter by Department
```bash
curl -X GET "https://api.example.com/api/employee/employees/?department=IT&is_active=true" \
  -H "Authorization: Bearer your-token"
```

### 6. Group by Department
```bash
curl -X GET "https://api.example.com/api/employee/employees/?groupby_field=employee_work_info__department_id" \
  -H "Authorization: Bearer your-token"
```

### 7. Advanced Search with Pagination
```bash
curl -X GET "https://api.example.com/api/employee/list/employees/?search=john&page=1" \
  -H "Authorization: Bearer your-token"
```

### 8. Bulk Update Employees
```bash
curl -X PUT "https://api.example.com/api/employee/employee-bulk-update/" \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "employees": [
      {"id": 1, "phone": "+1111111111"},
      {"id": 2, "address": "New Address"}
    ]
  }'
```

## Response Formats

### Success Response (List)
```json
{
  "count": 150,
  "next": "https://api.example.com/api/employee/employees/?page=2",
  "previous": null,
  "results": [...]
}
```

### Success Response (Single)
```json
{
  "id": 1,
  "employee_first_name": "John",
  "employee_last_name": "Doe",
  "email": "<EMAIL>",
  ...
}
```

### Error Response
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "email": ["This field is required."]
    }
  }
}
```

## HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `500` - Internal Server Error

## Required Permissions
- `employee.view_employee` - View employee data
- `employee.add_employee` - Create employees
- `employee.change_employee` - Update employees
- `employee.delete_employee` - Delete employees

## Rate Limits
- Authenticated: 1000 requests/hour
- Anonymous: 100 requests/hour

## Field Validation Rules
- `email`: Must be unique
- `badge_id`: Auto-generated
- `phone`: Valid phone format
- `dob`: Cannot be future date
- `salary`: Must be positive

## Hierarchical Access
- **Employee**: Own data only
- **Manager**: Own + subordinates
- **HR**: All employees
- **Admin**: Unrestricted

---

For detailed documentation, see: [Employee Module API Documentation](employee_module_api_documentation.md)
