# Attendance Module API Documentation

## 📋 Overview

The Attendance module provides comprehensive APIs for managing attendance operations in the Eaglora HRMS system.

## 🔗 Base URL
```
/api/attendance/
```

## 📑 Available Endpoints

| Endpoint | Method | Description | File |
|----------|--------|-------------|------|
| `/clock-in/` | POST | Employee clock in operation with GPS and face recognition | [clock-in.md](clock-in.md) |
| `/clock-out/` | POST | Employee clock out operation with validation | [clock-out.md](clock-out.md) |
| `/attendance/` | GET | List attendance records with filtering and pagination | [attendance-list.md](attendance-list.md) |
| `/attendance/` | POST | Create attendance record manually | [attendance-create.md](attendance-create.md) |
| `/attendance/{id}` | GET/PUT | Get or update specific attendance record | [attendance-detail.md](attendance-detail.md) |
| `/attendance/list/{type}` | GET | List attendance records by specific type | [attendance-list-by-type.md](attendance-list-by-type.md) |
| `/attendance-validate/{id}` | POST | Validate attendance record | [attendance-validate.md](attendance-validate.md) |
| `/attendance-request/` | GET | List attendance correction requests | [attendance-request-list.md](attendance-request-list.md) |
| `/attendance-request/` | POST | Create attendance correction request | [attendance-request-create.md](attendance-request-create.md) |
| `/attendance-request/{id}` | GET/PUT | Get or update attendance request | [attendance-request-detail.md](attendance-request-detail.md) |
| `/attendance-request-approve/{id}` | POST | Approve attendance correction request | [attendance-request-approve.md](attendance-request-approve.md) |
| `/attendance-request-cancel/{id}` | POST | Cancel attendance correction request | [attendance-request-cancel.md](attendance-request-cancel.md) |
| `/overtime-approve/{id}` | POST | Approve overtime request | [overtime-approve.md](overtime-approve.md) |
| `/attendance-hour-account/{id}/` | GET/PUT | Get or update attendance hour account | [attendance-hour-account-detail.md](attendance-hour-account-detail.md) |
| `/attendance-hour-account/` | GET | List attendance hour accounts | [attendance-hour-account-list.md](attendance-hour-account-list.md) |
| `/late-come-early-out-view/` | GET | View late arrivals and early departures | [late-come-early-out-view.md](late-come-early-out-view.md) |
| `/attendance-activity/` | GET | View attendance activity logs | [attendance-activity.md](attendance-activity.md) |
| `/today-attendance/` | GET | Get today's attendance summary | [today-attendance.md](today-attendance.md) |
| `/offline-employees/count/` | GET | Get count of offline employees | [offline-employees-count.md](offline-employees-count.md) |
| `/offline-employees/list/` | GET | List offline employees | [offline-employees-list.md](offline-employees-list.md) |
| `/permission-check/attendance` | GET | Check attendance permissions for user | [attendance-permission-check.md](attendance-permission-check.md) |
| `/checking-in` | GET | Check current clock-in status | [checking-status.md](checking-status.md) |
| `/offline-employee-mail-send` | POST | Send mail to offline employees | [offline-employee-mail-send.md](offline-employee-mail-send.md) |
| `/converted-mail-template` | GET | Get converted mail template for attendance | [converted-mail-template.md](converted-mail-template.md) |
| `/mail-templates` | GET | List attendance mail templates | [mail-templates.md](mail-templates.md) |

## 🔐 Authentication & Permissions

All endpoints require JWT authentication:

```http
Authorization: Bearer YOUR_JWT_TOKEN
```

## 🚀 Quick Start

### Basic Request Example
```bash
curl -X GET "/api/attendance/" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

## 📊 Use Cases

This module is commonly used for:
- **Access control**
- **Activity tracking**
- **Alerts**
- **Analytics**
- **Approval queue**
- **Attendance approval**
- **Attendance corrections**
- **Attendance reports**
- **Attendance updates**
- **Audit logs**
- **Automated notifications**
- **Biometric integration**
- **Bulk import**
- **Communication**
- **Communication setup**
- **Corrections**
- **Daily dashboard**
- **Dashboard metrics**
- **Email configuration**
- **Email templates**
- **Employee requests**
- **Employee tracking**
- **Filtered reports**
- **Follow-up actions**
- **HR analytics**
- **HR communication**
- **HR management**
- **HR monitoring**
- **HR reports**
- **HR validation**
- **HR workflow**
- **Hour reports**
- **Hour tracking**
- **Kiosk systems**
- **Manager actions**
- **Manager dashboards**
- **Manager overview**
- **Manual entry**
- **Mobile apps**
- **Notifications**
- **Overtime approval**
- **Overtime calculation**
- **Overtime tracking**
- **Payroll data**
- **Payroll integration**
- **Payroll preparation**
- **Performance monitoring**
- **Permission validation**
- **Processing**
- **Punctuality tracking**
- **Quality control**
- **Real-time monitoring**
- **Real-time tracking**
- **Real-time updates**
- **Record details**
- **Request approval**
- **Request cancellation**
- **Request details**
- **Request management**
- **Self-service**
- **Status updates**
- **Status verification**
- **System integration**
- **System monitoring**
- **Template management**
- **Time tracking**
- **Type-specific views**
- **UI customization**
- **Web applications**
- **Workflow management**

## 🔄 Related Modules

- [Employee Module](../employee/README.md)
- [Attendance Module](../attendance/README.md)
- [Leave Module](../leave/README.md)
- [Payroll Module](../payroll/README.md)

---

**Navigate to specific endpoints using the links above.**
