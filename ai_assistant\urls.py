"""
URL configuration for AI Assistant
"""

from django.urls import path
from . import views

app_name = 'ai_assistant'

urlpatterns = [
    # Main chat interface
    path('', views.ChatView.as_view(), name='chat'),
    path('chat/', views.ChatView.as_view(), name='chat_interface'),
    
    # API endpoints
    path('api/message/', views.chat_message, name='chat_message'),
    path('api/upload/', views.file_upload, name='file_upload'),
    path('api/history/', views.chat_history, name='chat_history'),
    path('api/direct-call/', views.direct_api_call, name='direct_api_call'),
    path('api/settings/', views.ai_settings, name='ai_settings'),
    path('api/memory/', views.conversation_memory, name='conversation_memory'),
    
    # HRMS integration endpoints
    path('api/employee/search/', views.employee_search, name='employee_search'),
    path('api/leave/apply/', views.quick_leave_apply, name='quick_leave_apply'),
    path('api/attendance/status/', views.attendance_status, name='attendance_status'),
    path('api/attendance/clock/', views.clock_action, name='clock_action'),

    # Demo and testing
    path('demo/navigation/', views.navigation_demo, name='navigation_demo'),
    path('demo/widget/', views.floating_widget_demo, name='floating_widget_demo'),
]
