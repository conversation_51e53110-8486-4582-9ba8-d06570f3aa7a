# Leave Module API Documentation

## 📋 Overview

The Leave module provides comprehensive APIs for managing leave operations in the Eaglora HRMS system.

## 🔗 Base URL
```
/api/leave/
```

## 📑 Available Endpoints

| Endpoint | Method | Description | File |
|----------|--------|-------------|------|
| `/leave-request/` | GET | List leave requests with filtering and pagination | [leave-request-list.md](leave-request-list.md) |
| `/leave-request/` | POST | Create new leave request | [leave-request-create.md](leave-request-create.md) |
| `/leave-request/{id}/` | GET/PUT | Get or update specific leave request | [leave-request-detail.md](leave-request-detail.md) |
| `/leave-request-approve/{id}/` | POST | Approve leave request | [leave-request-approve.md](leave-request-approve.md) |
| `/leave-request-cancel/{id}/` | POST | Cancel leave request | [leave-request-cancel.md](leave-request-cancel.md) |
| `/leave-request-reject/{id}/` | POST | Reject leave request | [leave-request-reject.md](leave-request-reject.md) |
| `/leave-type/` | GET | List all leave types | [leave-type-list.md](leave-type-list.md) |
| `/leave-type/` | POST | Create new leave type | [leave-type-create.md](leave-type-create.md) |
| `/leave-type/{id}/` | GET/PUT | Get or update leave type | [leave-type-detail.md](leave-type-detail.md) |
| `/leave-allocation/` | GET | List employee leave allocations | [leave-allocation-list.md](leave-allocation-list.md) |
| `/leave-allocation/` | POST | Create leave allocation for employee | [leave-allocation-create.md](leave-allocation-create.md) |
| `/leave-allocation/{id}/` | GET/PUT | Get or update leave allocation | [leave-allocation-detail.md](leave-allocation-detail.md) |
| `/leave-allocation-request/` | GET | List leave allocation requests | [leave-allocation-request-list.md](leave-allocation-request-list.md) |
| `/leave-allocation-request/` | POST | Create leave allocation request | [leave-allocation-request-create.md](leave-allocation-request-create.md) |
| `/leave-allocation-request/{id}/` | GET/PUT | Get or update leave allocation request | [leave-allocation-request-detail.md](leave-allocation-request-detail.md) |
| `/leave-allocation-request-approve/{id}/` | POST | Approve leave allocation request | [leave-allocation-request-approve.md](leave-allocation-request-approve.md) |
| `/leave-allocation-request-cancel/{id}/` | POST | Cancel leave allocation request | [leave-allocation-request-cancel.md](leave-allocation-request-cancel.md) |
| `/leave-allocation-request-reject/{id}/` | POST | Reject leave allocation request | [leave-allocation-request-reject.md](leave-allocation-request-reject.md) |
| `/holiday/` | GET | List company holidays | [holiday-list.md](holiday-list.md) |
| `/holiday/` | POST | Create company holiday | [holiday-create.md](holiday-create.md) |
| `/holiday/{id}/` | GET/PUT | Get or update holiday | [holiday-detail.md](holiday-detail.md) |
| `/leave-encashment/` | GET | List leave encashment records | [leave-encashment-list.md](leave-encashment-list.md) |
| `/leave-encashment/` | POST | Create leave encashment record | [leave-encashment-create.md](leave-encashment-create.md) |
| `/leave-encashment/{id}/` | GET/PUT | Get or update leave encashment | [leave-encashment-detail.md](leave-encashment-detail.md) |
| `/compensatory-leave/` | GET | List compensatory leave records | [compensatory-leave-list.md](compensatory-leave-list.md) |
| `/compensatory-leave/` | POST | Create compensatory leave record | [compensatory-leave-create.md](compensatory-leave-create.md) |
| `/compensatory-leave/{id}/` | GET/PUT | Get or update compensatory leave | [compensatory-leave-detail.md](compensatory-leave-detail.md) |
| `/restricted-holiday/` | GET | List restricted holidays | [restricted-holiday-list.md](restricted-holiday-list.md) |
| `/restricted-holiday/` | POST | Create restricted holiday | [restricted-holiday-create.md](restricted-holiday-create.md) |
| `/restricted-holiday/{id}/` | GET/PUT | Get or update restricted holiday | [restricted-holiday-detail.md](restricted-holiday-detail.md) |
| `/leave-clashing/` | GET | List leave clashing records | [leave-clashing-list.md](leave-clashing-list.md) |
| `/leave-clashing/` | POST | Create leave clashing record | [leave-clashing-create.md](leave-clashing-create.md) |
| `/leave-clashing/{id}/` | GET/PUT | Get or update leave clashing record | [leave-clashing-detail.md](leave-clashing-detail.md) |

## 🔐 Authentication & Permissions

All endpoints require JWT authentication:

```http
Authorization: Bearer YOUR_JWT_TOKEN
```

## 🚀 Quick Start

### Basic Request Example
```bash
curl -X GET "/api/leave/" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

## 📊 Use Cases

This module is commonly used for:
- **Additional leave**
- **Allocation processing**
- **Allocation requests**
- **Allocation updates**
- **Annual allocation**
- **Annual setup**
- **Approval queue**
- **Balance management**
- **Balance tracking**
- **Calendar management**
- **Calendar updates**
- **Comp leave allocation**
- **Comp leave management**
- **Comp leave tracking**
- **Company events**
- **Configuration**
- **Conflict detection**
- **Conflict resolution**
- **Conflict tracking**
- **Employee actions**
- **Employee benefits**
- **Employee choice**
- **Employee requests**
- **Employee self-service**
- **Encashment management**
- **Encashment tracking**
- **Event details**
- **Financial records**
- **HR adjustments**
- **HR approval**
- **HR configuration**
- **HR dashboard**
- **HR decisions**
- **HR entry**
- **HR management**
- **HR processing**
- **HR rejection**
- **HR setup**
- **Holiday calendar**
- **Holiday management**
- **Holiday setup**
- **Leave applications**
- **Leave balance**
- **Leave categories**
- **Leave configuration**
- **Leave coordination**
- **Leave credits**
- **Leave encashment**
- **Leave management**
- **Leave modifications**
- **Leave planning**
- **Manager approval**
- **Manager decisions**
- **Manager rejection**
- **New employee**
- **Optional holidays**
- **Overtime compensation**
- **Payroll integration**
- **Payroll processing**
- **Policy changes**
- **Policy management**
- **Policy setup**
- **Policy updates**
- **Processing**
- **Record updates**
- **Request cancellation**
- **Request details**
- **Request management**
- **Request processing**
- **Special allocations**
- **Status management**
- **Status updates**
- **Team coordination**
- **Team management**
- **Type management**
- **Workflow**
- **Workflow management**

## 🔄 Related Modules

- [Employee Module](../employee/README.md)
- [Attendance Module](../attendance/README.md)
- [Leave Module](../leave/README.md)
- [Payroll Module](../payroll/README.md)

---

**Navigate to specific endpoints using the links above.**
