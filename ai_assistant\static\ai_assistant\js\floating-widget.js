/**
 * Floating AI Assistant Widget
 * Supports text, voice, and file inputs
 */

class FloatingAIWidget {
    constructor(options = {}) {
        this.options = {
            apiUrl: '/ai-assistant/api/message/',
            uploadUrl: '/ai-assistant/api/upload/',
            position: 'bottom-right',
            theme: 'light',
            enableVoice: true,
            enableFileUpload: true,
            maxFileSize: 10 * 1024 * 1024, // 10MB
            allowedFileTypes: ['image/*', 'application/pdf', '.doc', '.docx', '.txt'],
            ...options
        };
        
        this.isOpen = false;
        this.isRecording = false;
        this.mediaRecorder = null;
        this.audioChunks = [];
        this.recognition = null;
        
        this.init();
    }
    
    init() {
        this.createWidget();
        this.bindEvents();
        this.initVoiceRecognition();
        this.loadChatHistory();
    }
    
    createWidget() {
        const container = document.createElement('div');
        container.className = 'ai-widget-container';
        container.innerHTML = `
            <!-- Floating Button -->
            <button class="ai-widget-button" id="aiWidgetToggle" aria-label="Open AI Assistant">
                <svg class="ai-widget-icon" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
                <div class="ai-widget-badge" id="aiWidgetBadge" style="display: none;">1</div>
            </button>
            
            <!-- Chat Widget -->
            <div class="ai-chat-widget" id="aiChatWidget">
                <!-- Header -->
                <div class="ai-widget-header">
                    <div class="ai-avatar">🤖</div>
                    <div class="ai-info">
                        <h3>HR Assistant</h3>
                        <div class="ai-status">
                            <div class="status-dot"></div>
                            Online
                        </div>
                    </div>
                </div>
                
                <!-- Messages -->
                <div class="ai-messages-container" id="aiMessages">
                    <div class="ai-message bot">
                        <div class="ai-message-bubble">
                            👋 Hello! I'm your AI HR assistant. I can help you with employee profiles, leave requests, attendance, and more. How can I assist you today?
                        </div>
                        <div class="ai-message-time">${this.getCurrentTime()}</div>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div class="ai-quick-actions">
                        <button class="ai-quick-btn" data-message="Check my leave balance">Leave Balance</button>
                        <button class="ai-quick-btn" data-message="View my attendance">Attendance</button>
                        <button class="ai-quick-btn" data-message="Apply for leave">Apply Leave</button>
                        <button class="ai-quick-btn" data-message="Find employee profiles">Employee Search</button>
                    </div>
                    
                    <!-- Typing Indicator -->
                    <div class="ai-typing-indicator" id="aiTyping">
                        <div class="ai-typing-dots">
                            <div class="ai-typing-dot"></div>
                            <div class="ai-typing-dot"></div>
                            <div class="ai-typing-dot"></div>
                        </div>
                    </div>
                </div>
                
                <!-- Input Container -->
                <div class="ai-input-container">
                    <div class="ai-input-wrapper">
                        <textarea 
                            class="ai-text-input" 
                            id="aiTextInput" 
                            placeholder="Type your message..."
                            rows="1"
                        ></textarea>
                        
                        <div class="ai-input-actions">
                            <!-- File Upload -->
                            <button class="ai-action-btn" id="aiFileBtn" title="Attach file" ${!this.options.enableFileUpload ? 'style="display:none"' : ''}>
                                <svg viewBox="0 0 24 24">
                                    <path d="M16.5 6v11.5c0 2.21-1.79 4-4 4s-4-1.79-4-4V5c0-1.38 1.12-2.5 2.5-2.5s2.5 1.12 2.5 2.5v10.5c0 .55-.45 1-1 1s-1-.45-1-1V6H10v9.5c0 1.38 1.12 2.5 2.5 2.5s2.5-1.12 2.5-2.5V5c0-2.21-1.79-4-4-4S7 2.79 7 5v12.5c0 3.04 2.46 5.5 5.5 5.5s5.5-2.46 5.5-5.5V6h-1.5z"/>
                                </svg>
                            </button>
                            
                            <!-- Voice Input -->
                            <button class="ai-action-btn" id="aiVoiceBtn" title="Voice input" ${!this.options.enableVoice ? 'style="display:none"' : ''}>
                                <svg viewBox="0 0 24 24">
                                    <path d="M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72h-1.7z"/>
                                </svg>
                            </button>
                            
                            <!-- Send Button -->
                            <button class="ai-action-btn" id="aiSendBtn" title="Send message">
                                <svg viewBox="0 0 24 24">
                                    <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    
                    <!-- File Input -->
                    <input type="file" class="ai-file-input" id="aiFileInput" multiple 
                           accept="${this.options.allowedFileTypes.join(',')}" />
                    
                    <!-- File Preview -->
                    <div class="ai-file-preview" id="aiFilePreview"></div>
                    
                    <!-- Voice Recording -->
                    <div class="ai-voice-recording" id="aiVoiceRecording">
                        <div class="ai-voice-wave"></div>
                        Recording... Click to stop
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(container);
        this.container = container;
        this.widget = container.querySelector('#aiChatWidget');
        this.button = container.querySelector('#aiWidgetToggle');
        this.messagesContainer = container.querySelector('#aiMessages');
        this.textInput = container.querySelector('#aiTextInput');
    }
    
    bindEvents() {
        // Toggle widget
        this.button.addEventListener('click', () => this.toggleWidget());
        
        // Send message
        this.container.querySelector('#aiSendBtn').addEventListener('click', () => this.sendMessage());
        
        // Enter key to send
        this.textInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        
        // Auto-resize textarea
        this.textInput.addEventListener('input', () => this.autoResizeTextarea());
        
        // File upload
        if (this.options.enableFileUpload) {
            this.container.querySelector('#aiFileBtn').addEventListener('click', () => {
                this.container.querySelector('#aiFileInput').click();
            });
            
            this.container.querySelector('#aiFileInput').addEventListener('change', (e) => {
                this.handleFileUpload(e.target.files);
            });
        }
        
        // Voice input
        if (this.options.enableVoice) {
            this.container.querySelector('#aiVoiceBtn').addEventListener('click', () => {
                this.toggleVoiceRecording();
            });
        }
        
        // Quick actions
        this.container.querySelectorAll('.ai-quick-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const message = btn.dataset.message;
                this.sendMessage(message);
            });
        });
        
        // Close widget when clicking outside
        document.addEventListener('click', (e) => {
            if (!this.container.contains(e.target) && this.isOpen) {
                this.closeWidget();
            }
        });
    }
    
    initVoiceRecognition() {
        if (!this.options.enableVoice) return;
        
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            this.recognition = new SpeechRecognition();
            this.recognition.continuous = false;
            this.recognition.interimResults = false;
            this.recognition.lang = 'en-US';
            
            this.recognition.onresult = (event) => {
                const transcript = event.results[0][0].transcript;
                this.textInput.value = transcript;
                this.sendMessage();
            };
            
            this.recognition.onerror = (event) => {
                console.error('Speech recognition error:', event.error);
                this.stopVoiceRecording();
            };
            
            this.recognition.onend = () => {
                this.stopVoiceRecording();
            };
        }
    }
    
    toggleWidget() {
        if (this.isOpen) {
            this.closeWidget();
        } else {
            this.openWidget();
        }
    }
    
    openWidget() {
        this.isOpen = true;
        this.widget.classList.add('active');
        this.button.classList.add('active');
        this.textInput.focus();
        this.hideBadge();
    }
    
    closeWidget() {
        this.isOpen = false;
        this.widget.classList.remove('active');
        this.button.classList.remove('active');
        this.stopVoiceRecording();
    }
    
    showBadge(count = 1) {
        const badge = this.container.querySelector('#aiWidgetBadge');
        badge.textContent = count;
        badge.style.display = 'flex';
    }
    
    hideBadge() {
        const badge = this.container.querySelector('#aiWidgetBadge');
        badge.style.display = 'none';
    }
    
    autoResizeTextarea() {
        const textarea = this.textInput;
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 100) + 'px';
    }
    
    getCurrentTime() {
        return new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
    
    addMessage(content, type = 'user', options = {}) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `ai-message ${type}`;
        
        let messageContent = '';
        if (typeof content === 'string') {
            messageContent = content;
        } else if (content.type === 'file') {
            messageContent = `📎 ${content.name}`;
        } else if (content.type === 'voice') {
            messageContent = `🎤 Voice message`;
        }
        
        messageDiv.innerHTML = `
            <div class="ai-message-bubble">${messageContent}</div>
            <div class="ai-message-time">${this.getCurrentTime()}</div>
        `;
        
        // Insert before typing indicator
        const typingIndicator = this.container.querySelector('#aiTyping');
        this.messagesContainer.insertBefore(messageDiv, typingIndicator);
        
        this.scrollToBottom();
        return messageDiv;
    }
    
    showTyping() {
        this.container.querySelector('#aiTyping').style.display = 'block';
        this.scrollToBottom();
    }
    
    hideTyping() {
        this.container.querySelector('#aiTyping').style.display = 'none';
    }
    
    scrollToBottom() {
        setTimeout(() => {
            this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
        }, 100);
    }
    
    async sendMessage(message = null) {
        const text = message || this.textInput.value.trim();
        if (!text) return;
        
        // Add user message
        this.addMessage(text, 'user');
        
        // Clear input
        this.textInput.value = '';
        this.autoResizeTextarea();
        
        // Show typing indicator
        this.showTyping();
        
        try {
            const response = await fetch(this.options.apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken(),
                },
                body: JSON.stringify({
                    message: text,
                    session_id: this.getSessionId()
                })
            });
            
            const data = await response.json();
            
            // Hide typing indicator
            this.hideTyping();
            
            // Add bot response
            this.addMessage(data.message || 'Sorry, I encountered an error.', 'bot');
            
            // Handle navigation
            if (data.action === 'navigate' && data.url) {
                this.handleNavigation(data);
            }
            
        } catch (error) {
            console.error('Error sending message:', error);
            this.hideTyping();
            this.addMessage('Sorry, there was an error processing your request.', 'bot');
        }
    }
    
    handleNavigation(data) {
        // Show navigation message
        setTimeout(() => {
            const targetUrl = this.buildUrlWithFilters(data.url, data.filters);
            window.location.href = targetUrl;
        }, 1500);
    }
    
    buildUrlWithFilters(url, filters = {}) {
        if (!filters || Object.keys(filters).length === 0) {
            return url;
        }
        
        const urlObj = new URL(url, window.location.origin);
        Object.keys(filters).forEach(key => {
            if (filters[key] !== null && filters[key] !== undefined && filters[key] !== '') {
                urlObj.searchParams.set(key, filters[key]);
            }
        });
        
        return urlObj.pathname + urlObj.search;
    }
    
    getCSRFToken() {
        const token = document.querySelector('[name=csrfmiddlewaretoken]');
        return token ? token.value : '';
    }
    
    getSessionId() {
        let sessionId = localStorage.getItem('ai_session_id');
        if (!sessionId) {
            sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            localStorage.setItem('ai_session_id', sessionId);
        }
        return sessionId;
    }
    
    loadChatHistory() {
        // Load previous chat history from localStorage or API
        const history = localStorage.getItem('ai_chat_history');
        if (history) {
            try {
                const messages = JSON.parse(history);
                messages.slice(-5).forEach(msg => {
                    this.addMessage(msg.content, msg.type);
                });
            } catch (e) {
                console.error('Error loading chat history:', e);
            }
        }
    }
    
    saveChatHistory(message, type) {
        try {
            let history = JSON.parse(localStorage.getItem('ai_chat_history') || '[]');
            history.push({ content: message, type, timestamp: Date.now() });

            // Keep only last 50 messages
            if (history.length > 50) {
                history = history.slice(-50);
            }

            localStorage.setItem('ai_chat_history', JSON.stringify(history));
        } catch (e) {
            console.error('Error saving chat history:', e);
        }
    }

    // Voice Recording Methods
    toggleVoiceRecording() {
        if (this.isRecording) {
            this.stopVoiceRecording();
        } else {
            this.startVoiceRecording();
        }
    }

    async startVoiceRecording() {
        try {
            // Try speech recognition first
            if (this.recognition) {
                this.recognition.start();
                this.isRecording = true;
                this.showVoiceRecording();
                this.container.querySelector('#aiVoiceBtn').classList.add('active');
                return;
            }

            // Fallback to audio recording
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            this.mediaRecorder = new MediaRecorder(stream);
            this.audioChunks = [];

            this.mediaRecorder.ondataavailable = (event) => {
                this.audioChunks.push(event.data);
            };

            this.mediaRecorder.onstop = () => {
                const audioBlob = new Blob(this.audioChunks, { type: 'audio/wav' });
                this.uploadAudioFile(audioBlob);
            };

            this.mediaRecorder.start();
            this.isRecording = true;
            this.showVoiceRecording();
            this.container.querySelector('#aiVoiceBtn').classList.add('active');

        } catch (error) {
            console.error('Error starting voice recording:', error);
            alert('Unable to access microphone. Please check permissions.');
        }
    }

    stopVoiceRecording() {
        if (this.recognition) {
            this.recognition.stop();
        }

        if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
            this.mediaRecorder.stop();
            this.mediaRecorder.stream.getTracks().forEach(track => track.stop());
        }

        this.isRecording = false;
        this.hideVoiceRecording();
        this.container.querySelector('#aiVoiceBtn').classList.remove('active');
    }

    showVoiceRecording() {
        this.container.querySelector('#aiVoiceRecording').style.display = 'flex';
    }

    hideVoiceRecording() {
        this.container.querySelector('#aiVoiceRecording').style.display = 'none';
    }

    async uploadAudioFile(audioBlob) {
        const formData = new FormData();
        formData.append('audio', audioBlob, 'voice_message.wav');
        formData.append('type', 'voice');

        try {
            const response = await fetch(this.options.uploadUrl, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': this.getCSRFToken(),
                },
                body: formData
            });

            const data = await response.json();

            if (data.success) {
                // Add voice message
                this.addMessage({ type: 'voice', name: 'Voice message' }, 'user');

                // Process transcription if available
                if (data.transcription) {
                    this.sendMessage(data.transcription);
                }
            } else {
                throw new Error(data.error || 'Upload failed');
            }

        } catch (error) {
            console.error('Error uploading audio:', error);
            this.addMessage('Sorry, voice message upload failed.', 'bot');
        }
    }

    // File Upload Methods
    handleFileUpload(files) {
        if (!files || files.length === 0) return;

        const validFiles = Array.from(files).filter(file => this.validateFile(file));

        if (validFiles.length === 0) {
            alert('Please select valid files.');
            return;
        }

        this.showFilePreview(validFiles);
        this.uploadFiles(validFiles);
    }

    validateFile(file) {
        // Check file size
        if (file.size > this.options.maxFileSize) {
            alert(`File ${file.name} is too large. Maximum size is ${this.options.maxFileSize / 1024 / 1024}MB.`);
            return false;
        }

        // Check file type
        const isValidType = this.options.allowedFileTypes.some(type => {
            if (type.includes('*')) {
                return file.type.startsWith(type.replace('*', ''));
            }
            return file.type === type || file.name.toLowerCase().endsWith(type);
        });

        if (!isValidType) {
            alert(`File type ${file.type} is not allowed.`);
            return false;
        }

        return true;
    }

    showFilePreview(files) {
        const preview = this.container.querySelector('#aiFilePreview');
        preview.innerHTML = files.map(file =>
            `📎 ${file.name} (${(file.size / 1024).toFixed(1)} KB)`
        ).join('<br>');
        preview.style.display = 'block';

        setTimeout(() => {
            preview.style.display = 'none';
        }, 3000);
    }

    async uploadFiles(files) {
        for (const file of files) {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('type', 'file');

            try {
                // Add file message
                this.addMessage({ type: 'file', name: file.name }, 'user');

                const response = await fetch(this.options.uploadUrl, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': this.getCSRFToken(),
                    },
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    // Process file content if available
                    if (data.content) {
                        this.sendMessage(`Analyze this file: ${data.content}`);
                    } else {
                        this.addMessage(`File ${file.name} uploaded successfully.`, 'bot');
                    }
                } else {
                    throw new Error(data.error || 'Upload failed');
                }

            } catch (error) {
                console.error('Error uploading file:', error);
                this.addMessage(`Sorry, failed to upload ${file.name}.`, 'bot');
            }
        }
    }
}

// Initialize widget when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.aiWidget = new FloatingAIWidget({
        apiUrl: '/ai-assistant/api/message/',
        uploadUrl: '/ai-assistant/api/upload/',
        enableVoice: true,
        enableFileUpload: true,
        maxFileSize: 10 * 1024 * 1024, // 10MB
        allowedFileTypes: ['image/*', 'application/pdf', '.doc', '.docx', '.txt', '.csv']
    });
});
