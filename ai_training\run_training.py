#!/usr/bin/env python3
"""
Eaglora HRMS AI Training Pipeline

This script runs the complete AI training pipeline:
1. Generate 100,000+ training examples for each API endpoint
2. Train AI models on the generated data
3. Test and validate the trained models
4. Integrate with the existing AI assistant
"""

import os
import sys
import time
import json
from datetime import datetime
from typing import Dict, Any

def print_banner():
    """Print training pipeline banner"""
    print("🤖 EAGLORA HRMS AI TRAINING PIPELINE")
    print("=" * 60)
    print("🎯 Goal: Train AI on ALL 186 API endpoints")
    print("📊 Target: 100,000+ examples per endpoint")
    print("🧠 Models: Intent Classification + Search Extraction")
    print("🔗 Integration: Natural Language → API Calls")
    print("=" * 60)
    print()

def check_dependencies():
    """Check if all required dependencies are installed"""
    print("🔍 Checking dependencies...")
    
    required_packages = [
        'scikit-learn',
        'pandas',
        'numpy',
        'joblib',
        'requests'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"  ✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"  ❌ {package}")
    
    if missing_packages:
        print(f"\n📦 Installing missing packages: {', '.join(missing_packages)}")
        os.system(f"pip install {' '.join(missing_packages)}")
        print("✅ Dependencies installed!")
    else:
        print("✅ All dependencies satisfied!")
    
    print()

def run_training_data_generation():
    """Run the training data generation script"""
    print("📊 STEP 1: Generating Training Data")
    print("-" * 40)
    
    try:
        # Import and run the training data generator
        from api_training_generator import EagloraAITrainingGenerator
        
        print("🚀 Starting training data generation...")
        generator = EagloraAITrainingGenerator()
        
        # Generate all training data
        training_data = generator.generate_all_training_data()
        
        # Save training data
        generator.save_training_data(training_data)
        
        print("✅ Training data generation completed!")
        return True
        
    except Exception as e:
        print(f"❌ Training data generation failed: {str(e)}")
        return False

def run_model_training():
    """Run the AI model training script"""
    print("\n🧠 STEP 2: Training AI Models")
    print("-" * 40)
    
    try:
        # Import and run the model trainer
        from ai_model_trainer import EagloraAIModelTrainer
        
        print("🚀 Starting AI model training...")
        trainer = EagloraAIModelTrainer()
        
        # Train all models
        results = trainer.train_all_models()
        
        # Save models
        trainer.save_models()
        
        # Test predictions
        trainer.test_model_predictions()
        
        print("✅ AI model training completed!")
        return True, results
        
    except Exception as e:
        print(f"❌ AI model training failed: {str(e)}")
        return False, {}

def run_integration_test():
    """Test the AI integration"""
    print("\n🔗 STEP 3: Testing AI Integration")
    print("-" * 40)
    
    try:
        # Import and test the integration
        from ai_integration import EagloraAIIntegration
        
        print("🚀 Testing AI integration...")
        ai = EagloraAIIntegration()
        
        if not ai.is_loaded:
            print("❌ AI models not loaded properly")
            return False
        
        # Test with sample queries
        test_queries = [
            "find employee john",
            "search for staff named jane doe",
            "show me all employees in IT department",
            "clock in now",
            "I want to clock out",
            "create new employee record",
            "list all pending leave requests",
            "approve leave request for john",
            "show today's attendance report",
            "find assets assigned to IT",
            "get my latest payslip",
            "show company policies",
            "search for notifications about leave",
            "help me find employee with ID 123"
        ]
        
        print("🧪 Running test queries...")
        successful_predictions = 0
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n  Test {i}: {query}")
            result = ai.predict_api_call(query)
            
            if result.get("success", False):
                print(f"    ✅ {result['method']} {result['api_endpoint']}")
                print(f"    📊 Confidence: {result['confidence']:.3f}")
                print(f"    🎯 Module: {result['module']} | Action: {result['action']}")
                if result.get('search_term'):
                    print(f"    🔍 Search: {result['search_term']}")
                successful_predictions += 1
            else:
                print(f"    ❌ Error: {result.get('error', 'Unknown error')}")
        
        success_rate = (successful_predictions / len(test_queries)) * 100
        print(f"\n📊 Test Results: {successful_predictions}/{len(test_queries)} successful ({success_rate:.1f}%)")
        
        if success_rate >= 80:
            print("✅ AI integration test passed!")
            return True
        else:
            print("⚠️  AI integration test needs improvement")
            return False
        
    except Exception as e:
        print(f"❌ AI integration test failed: {str(e)}")
        return False

def generate_training_report(training_results: Dict[str, Any]):
    """Generate a comprehensive training report"""
    print("\n📋 STEP 4: Generating Training Report")
    print("-" * 40)
    
    try:
        # Load training statistics
        stats_file = "ai_training/data/training_statistics.json"
        if os.path.exists(stats_file):
            with open(stats_file, 'r') as f:
                stats = json.load(f)
        else:
            stats = {}
        
        # Create comprehensive report
        report = {
            "training_date": datetime.now().isoformat(),
            "pipeline_status": "completed",
            "training_statistics": stats,
            "model_performance": training_results,
            "integration_status": "tested",
            "deployment_ready": True
        }
        
        # Save report
        os.makedirs("ai_training/reports", exist_ok=True)
        report_file = f"ai_training/reports/training_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        # Print summary
        print("📊 TRAINING PIPELINE SUMMARY")
        print("=" * 50)
        
        if stats:
            print(f"📁 Total Modules: {stats.get('total_modules', 'N/A')}")
            print(f"🔗 Total Endpoints: {stats['overall_stats'].get('total_endpoints', 'N/A')}")
            print(f"📚 Total Training Examples: {stats['overall_stats'].get('total_examples', 'N/A'):,}")
            print(f"📈 Avg Examples per Endpoint: {stats['overall_stats'].get('average_examples_per_endpoint', 'N/A'):,}")
        
        if training_results:
            if 'intent_classifier' in training_results:
                accuracy = training_results['intent_classifier'].get('accuracy', 0)
                print(f"🎯 Intent Classification Accuracy: {accuracy:.1%}")
            
            if 'search_extractor' in training_results:
                search_accuracy = training_results['search_extractor'].get('accuracy', 0)
                print(f"🔍 Search Extraction Accuracy: {search_accuracy:.1%}")
        
        print(f"📄 Report saved: {report_file}")
        print("✅ Training report generated!")
        
        return True
        
    except Exception as e:
        print(f"❌ Report generation failed: {str(e)}")
        return False

def update_ai_assistant_integration():
    """Update the existing AI assistant to use the trained models"""
    print("\n🔧 STEP 5: Updating AI Assistant Integration")
    print("-" * 40)
    
    try:
        # Check if AI assistant views exist
        ai_views_file = "ai_assistant/views.py"
        
        if not os.path.exists(ai_views_file):
            print("⚠️  AI assistant views.py not found, skipping integration update")
            return True
        
        print("🔄 Updating AI assistant integration...")
        
        # Create integration code snippet
        integration_code = '''
# AI Training Integration
from ai_training.ai_integration import ai_integration, predict_and_execute

def enhanced_chat_message(request):
    """Enhanced chat message handler with trained AI models"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            user_message = data.get('message', '')
            
            # Try AI-powered API prediction first
            if ai_integration.is_loaded:
                prediction_result = predict_and_execute(user_message)
                
                if prediction_result.get('success', False):
                    # Return structured API response
                    return JsonResponse({
                        'response': 'I found what you\'re looking for!',
                        'api_call': prediction_result['prediction'],
                        'data': prediction_result['execution'].get('data'),
                        'type': 'api_result'
                    })
            
            # Fallback to regular chat processing
            # ... existing chat logic ...
            
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
'''
        
        # Save integration snippet
        integration_file = "ai_training/ai_assistant_integration.py"
        with open(integration_file, 'w') as f:
            f.write(integration_code)
        
        print(f"✅ Integration code saved to: {integration_file}")
        print("📝 Manual integration required in ai_assistant/views.py")
        print("🔗 Import and use the enhanced_chat_message function")
        
        return True
        
    except Exception as e:
        print(f"❌ AI assistant integration update failed: {str(e)}")
        return False

def main():
    """Main training pipeline execution"""
    start_time = time.time()
    
    print_banner()
    
    # Step 0: Check dependencies
    check_dependencies()
    
    # Step 1: Generate training data
    if not run_training_data_generation():
        print("❌ Training pipeline failed at data generation step")
        sys.exit(1)
    
    # Step 2: Train AI models
    training_success, training_results = run_model_training()
    if not training_success:
        print("❌ Training pipeline failed at model training step")
        sys.exit(1)
    
    # Step 3: Test integration
    if not run_integration_test():
        print("⚠️  Integration test had issues, but continuing...")
    
    # Step 4: Generate report
    generate_training_report(training_results)
    
    # Step 5: Update AI assistant integration
    update_ai_assistant_integration()
    
    # Final summary
    end_time = time.time()
    duration = end_time - start_time
    
    print("\n🎉 TRAINING PIPELINE COMPLETED!")
    print("=" * 50)
    print(f"⏱️  Total Duration: {duration/60:.1f} minutes")
    print("📁 Generated Files:")
    print("  - ai_training/data/ (training data)")
    print("  - ai_training/models/ (trained models)")
    print("  - ai_training/reports/ (training reports)")
    print("  - ai_training/ai_assistant_integration.py (integration code)")
    print()
    print("🚀 NEXT STEPS:")
    print("1. Review training report for model performance")
    print("2. Integrate enhanced chat handler in AI assistant")
    print("3. Test with real user queries")
    print("4. Deploy to production environment")
    print()
    print("🎯 The AI is now trained on ALL 186 API endpoints!")
    print("🤖 Ready for natural language → API mapping!")

if __name__ == "__main__":
    main()
