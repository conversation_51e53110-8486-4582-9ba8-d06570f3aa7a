#!/usr/bin/env python3
"""
Simple test script to verify AI assistant navigation functionality
without requiring full Django setup
"""

import re
import sys
import os

# Add the project directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_employee_name_extraction():
    """Test employee name extraction logic"""
    print("Testing employee name extraction...")
    
    def extract_employee_name(message: str) -> str:
        """Extract employee name from the message"""
        # Common patterns for employee name extraction
        patterns = [
            r'(?:get|show|find)\s+([a-zA-Z\s]+)\'?s?\s+(?:profile|information|details)',  # "get sethu's profile"
            r'(?:profile|information|details)\s+(?:of|for)\s+([a-zA-Z\s]+)',  # "profile of john doe"
            r'(?:get|show|find)\s+([a-zA-Z\s]+)(?:\s+profile|\s+information|\s+details)',  # "get john doe profile"
            r'([a-zA-Z\s]+)(?:\s+profile|\s+information|\s+details)',  # "john doe profile"
            r'(?:employee|staff)\s+([a-zA-Z\s]+)',  # "employee john doe"
        ]
        
        message_lower = message.lower()
        for pattern in patterns:
            match = re.search(pattern, message_lower)
            if match:
                name = match.group(1).strip()
                # Filter out common words that aren't names
                exclude_words = {'profile', 'information', 'details', 'employee', 'staff', 'get', 'show', 'find', 'of', 'for', 'the', 'a', 'an'}
                name_words = [word for word in name.split() if word.lower() not in exclude_words]
                if name_words and len(' '.join(name_words)) > 2:
                    return ' '.join(name_words).title()
        
        return ""
    
    test_cases = [
        ("get sethu's profile", "Sethu"),
        ("show john doe profile", "John Doe"),
        ("profile of jane smith", "Jane Smith"),
        ("find employee john", "John"),
        ("get information for jane", "Jane"),
        ("john doe details", "John Doe"),
        ("employee jane information", "Jane"),
        ("get sethu profile", "Sethu"),
    ]
    
    passed = 0
    failed = 0
    
    for message, expected_name in test_cases:
        extracted_name = extract_employee_name(message)
        if extracted_name == expected_name:
            print(f"✓ PASS: '{message}' -> '{extracted_name}'")
            passed += 1
        else:
            print(f"✗ FAIL: '{message}' -> Expected: '{expected_name}', Got: '{extracted_name}'")
            failed += 1
    
    print(f"\nEmployee Name Extraction: {passed} passed, {failed} failed")
    return failed == 0

def test_navigation_logic():
    """Test navigation logic"""
    print("\nTesting navigation logic...")
    
    def simulate_employee_search(employee_name: str):
        """Simulate employee search results"""
        # Mock employee data
        employees = [
            {"id": 1, "first_name": "John", "last_name": "Doe", "full_name": "John Doe"},
            {"id": 2, "first_name": "Jane", "last_name": "Smith", "full_name": "Jane Smith"},
            {"id": 3, "first_name": "John", "last_name": "Wilson", "full_name": "John Wilson"},
            {"id": 4, "first_name": "Sethu", "last_name": "", "full_name": "Sethu"},
        ]
        
        # Search logic
        matches = []
        for emp in employees:
            if (employee_name.lower() in emp["first_name"].lower() or 
                employee_name.lower() in emp["last_name"].lower() or
                employee_name.lower() in emp["full_name"].lower()):
                matches.append(emp)
        
        return matches
    
    def handle_employee_profile_navigation(employee_name: str):
        """Handle navigation to employee profile based on search"""
        employees = simulate_employee_search(employee_name)
        
        if len(employees) == 1:
            # Single employee found - navigate directly to their profile
            employee = employees[0]
            return {
                'message': f"Navigating to {employee['full_name']}'s profile.",
                'action': 'navigate',
                'url': f'/employee/employee-view/{employee["id"]}/',
                'filters': {}
            }
        elif len(employees) > 1:
            # Multiple employees found - navigate to search results
            employee_names = [emp['full_name'] for emp in employees[:3]]
            names_text = ', '.join(employee_names)
            if len(employees) > 3:
                names_text += f" and {len(employees) - 3} more"
            
            return {
                'message': f"Found multiple employees matching '{employee_name}': {names_text}. Showing search results.",
                'action': 'navigate',
                'url': '/employee/employee-view/',
                'filters': {'search': employee_name}
            }
        else:
            # No employees found
            return {
                'message': f"No employee found with the name '{employee_name}'. Showing all employees.",
                'action': 'navigate',
                'url': '/employee/employee-view/',
                'filters': {'search': employee_name}
            }
    
    test_cases = [
        ("Sethu", "navigate", "/employee/employee-view/4/", "single match"),
        ("Jane", "navigate", "/employee/employee-view/2/", "single match"),
        ("John", "navigate", "/employee/employee-view/", "multiple matches"),
        ("NonExistent", "navigate", "/employee/employee-view/", "no matches"),
    ]
    
    passed = 0
    failed = 0
    
    for name, expected_action, expected_url_pattern, description in test_cases:
        result = handle_employee_profile_navigation(name)
        
        if (result['action'] == expected_action and 
            expected_url_pattern in result['url']):
            print(f"✓ PASS: '{name}' -> {description}")
            passed += 1
        else:
            print(f"✗ FAIL: '{name}' -> Expected action: {expected_action}, URL pattern: {expected_url_pattern}")
            print(f"         Got: action={result['action']}, url={result['url']}")
            failed += 1
    
    print(f"\nNavigation Logic: {passed} passed, {failed} failed")
    return failed == 0

def test_url_building():
    """Test URL building with filters"""
    print("\nTesting URL building with filters...")
    
    def build_url_with_filters(url, filters=None):
        """Build URL with query parameters from filters"""
        if not filters or not filters:
            return url
        
        query_params = []
        for key, value in filters.items():
            if value is not None and value != '':
                query_params.append(f"{key}={value}")
        
        if query_params:
            return f"{url}?{'&'.join(query_params)}"
        return url
    
    test_cases = [
        ("/employee/employee-view/", {}, "/employee/employee-view/"),
        ("/employee/employee-view/", {"search": "john"}, "/employee/employee-view/?search=john"),
        ("/employee/employee-view/", {"search": "john", "department": "IT"}, "/employee/employee-view/?search=john&department=IT"),
        ("/leave/user-leave-request/", {}, "/leave/user-leave-request/"),
    ]
    
    passed = 0
    failed = 0
    
    for url, filters, expected in test_cases:
        result = build_url_with_filters(url, filters)
        if result == expected:
            print(f"✓ PASS: URL building for {url} with {filters}")
            passed += 1
        else:
            print(f"✗ FAIL: Expected: {expected}, Got: {result}")
            failed += 1
    
    print(f"\nURL Building: {passed} passed, {failed} failed")
    return failed == 0

def main():
    """Run all tests"""
    print("AI Assistant Navigation Functionality Tests")
    print("=" * 50)
    
    all_passed = True
    
    # Run tests
    all_passed &= test_employee_name_extraction()
    all_passed &= test_navigation_logic()
    all_passed &= test_url_building()
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 ALL TESTS PASSED! Navigation functionality is working correctly.")
        return 0
    else:
        print("❌ SOME TESTS FAILED! Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
