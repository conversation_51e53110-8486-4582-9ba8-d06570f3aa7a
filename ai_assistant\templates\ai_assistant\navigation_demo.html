<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Assistant Navigation Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .chat-container {
            height: 500px;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow-y: auto;
            padding: 15px;
            background-color: #f8f9fa;
        }
        
        .message {
            margin-bottom: 15px;
            animation: fadeInUp 0.3s ease-out;
        }
        
        .user-message {
            text-align: right;
        }
        
        .bot-message {
            text-align: left;
        }
        
        .message-bubble {
            display: inline-block;
            padding: 10px 15px;
            border-radius: 18px;
            max-width: 70%;
            word-wrap: break-word;
        }
        
        .user-message .message-bubble {
            background-color: #007bff;
            color: white;
        }
        
        .bot-message .message-bubble {
            background-color: white;
            color: #333;
            border: 1px solid #ddd;
        }
        
        .navigation-indicator {
            opacity: 0.8;
        }
        
        .navigation-loading {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background-color: #e3f2fd;
            border-radius: 1rem;
            color: #1976d2;
            font-size: 0.9rem;
        }
        
        .spinner {
            width: 16px;
            height: 16px;
            border: 2px solid #e3f2fd;
            border-top: 2px solid #1976d2;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .demo-buttons {
            margin-top: 15px;
        }
        
        .demo-buttons .btn {
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <h2 class="text-center mb-4">AI Assistant Navigation Demo</h2>
                
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">HR Assistant Chat</h5>
                    </div>
                    <div class="card-body">
                        <div id="chat-messages" class="chat-container">
                            <div class="message bot-message">
                                <div class="message-bubble">
                                    Hello! I'm your AI HR assistant. I can help you navigate to employee profiles, leave information, and attendance records. Try asking me something like "get sethu's profile" or "show my leave balance".
                                </div>
                            </div>
                        </div>
                        
                        <div class="input-group mt-3">
                            <input type="text" id="message-input" class="form-control" placeholder="Type your message..." onkeypress="handleKeyPress(event)">
                            <button class="btn btn-primary" onclick="sendMessage()">Send</button>
                        </div>
                        
                        <div class="demo-buttons">
                            <h6>Try these examples:</h6>
                            <button class="btn btn-outline-primary btn-sm" onclick="sendDemoMessage('get sethu profile')">Get Sethu's Profile</button>
                            <button class="btn btn-outline-primary btn-sm" onclick="sendDemoMessage('show john doe information')">Show John Doe Info</button>
                            <button class="btn btn-outline-primary btn-sm" onclick="sendDemoMessage('check my leave balance')">Check Leave Balance</button>
                            <button class="btn btn-outline-primary btn-sm" onclick="sendDemoMessage('view my attendance')">View Attendance</button>
                            <button class="btn btn-outline-primary btn-sm" onclick="sendDemoMessage('apply for leave')">Apply for Leave</button>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-info mt-3">
                    <strong>Navigation Features:</strong>
                    <ul class="mb-0">
                        <li>Direct navigation to specific employee profiles when found</li>
                        <li>Search results page when multiple employees match</li>
                        <li>Automatic redirection to relevant HR pages</li>
                        <li>Visual navigation indicators</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const messagesContainer = document.getElementById('chat-messages');
        const messageInput = document.getElementById('message-input');
        
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
        
        function sendDemoMessage(message) {
            messageInput.value = message;
            sendMessage();
        }
        
        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;
            
            // Add user message
            addMessage(message, 'user');
            messageInput.value = '';
            
            // Simulate AI response with navigation
            setTimeout(() => {
                simulateAIResponse(message);
            }, 500);
        }
        
        function addMessage(text, sender) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;
            
            const bubbleDiv = document.createElement('div');
            bubbleDiv.className = 'message-bubble';
            bubbleDiv.textContent = text;
            
            messageDiv.appendChild(bubbleDiv);
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        function simulateAIResponse(userMessage) {
            const message = userMessage.toLowerCase();
            let response, action, url, filters = {};
            
            if (message.includes('profile') || message.includes('employee') || message.includes('information')) {
                if (message.includes('sethu')) {
                    response = "Navigating to Sethu's profile.";
                    action = 'navigate';
                    url = '/employee/employee-view/1/';
                } else if (message.includes('john')) {
                    response = "Found multiple employees matching 'John': John Doe, John Wilson. Showing search results.";
                    action = 'navigate';
                    url = '/employee/employee-view/';
                    filters = {search: 'john'};
                } else {
                    response = "I'll help you search for employee information.";
                    action = 'navigate';
                    url = '/employee/employee-view/';
                    filters = {search: userMessage};
                }
            } else if (message.includes('leave') && message.includes('balance')) {
                response = "Navigating to your leave balance information.";
                action = 'navigate';
                url = '/leave/user-leave-request/';
            } else if (message.includes('leave') && message.includes('apply')) {
                response = "Navigating to the leave application form.";
                action = 'navigate';
                url = '/leave/user-request-create/';
            } else if (message.includes('attendance')) {
                response = "Navigating to your attendance information.";
                action = 'navigate';
                url = '/attendance/attendance-view/';
            } else {
                response = "I understand you're asking about HR-related matters. Could you please be more specific?";
                action = 'message';
                url = '';
            }
            
            // Add bot response
            addMessage(response, 'bot');
            
            // Handle navigation
            if (action === 'navigate' && url) {
                showNavigationIndicator();
                setTimeout(() => {
                    const targetUrl = buildUrlWithFilters(url, filters);
                    console.log('Would navigate to:', targetUrl);
                    alert(`Demo: Would navigate to ${targetUrl}`);
                }, 1000);
            }
        }
        
        function showNavigationIndicator() {
            const indicatorDiv = document.createElement('div');
            indicatorDiv.className = 'message bot-message navigation-indicator';
            indicatorDiv.innerHTML = `
                <div class="message-bubble">
                    <div class="navigation-loading">
                        <div class="spinner"></div>
                        <span>Navigating...</span>
                    </div>
                </div>
            `;
            
            messagesContainer.appendChild(indicatorDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
            
            setTimeout(() => {
                if (indicatorDiv.parentNode) {
                    indicatorDiv.parentNode.removeChild(indicatorDiv);
                }
            }, 1500);
        }
        
        function buildUrlWithFilters(url, filters = {}) {
            if (!filters || Object.keys(filters).length === 0) {
                return url;
            }
            
            const urlObj = new URL(url, window.location.origin);
            Object.keys(filters).forEach(key => {
                if (filters[key] !== null && filters[key] !== undefined && filters[key] !== '') {
                    urlObj.searchParams.set(key, filters[key]);
                }
            });
            
            return urlObj.pathname + urlObj.search;
        }
    </script>
</body>
</html>
