# AI Assistant Navigation Guide

## Overview

The AI Assistant now includes intelligent navigation functionality that redirects users to relevant HRMS pages instead of just providing text responses. When users ask for employee profiles, leave information, or attendance data, the AI will automatically navigate them to the appropriate pages.

## Features

### 🎯 Smart Employee Profile Navigation
- **Single Match**: Direct navigation to employee profile page
- **Multiple Matches**: Navigation to search results with filters applied
- **No Matches**: Navigation to employee list with search term pre-filled

### 🔄 Automatic Page Redirection
- Leave balance requests → Leave management page
- Leave applications → Leave request form
- Attendance queries → Attendance dashboard

### 💬 Natural Language Processing
Supports various message formats:
- "get sethu's profile"
- "show john doe information"
- "find employee jane"
- "check my leave balance"
- "view attendance"

## How It Works

1. **User Input**: User types a natural language request
2. **Intent Recognition**: AI identifies the intent (employee profile, leave, attendance)
3. **Name Extraction**: For employee queries, extracts the employee name
4. **Database Search**: Searches for matching employees
5. **Smart Navigation**: Redirects to the most appropriate page
6. **Visual Feedback**: Shows loading indicator during navigation

## Configuration

### Disable Rasa (Use Mock Responses)
```bash
python manage.py configure_ai_assistant --disable-rasa
```

### Enable Rasa Server
```bash
python manage.py configure_ai_assistant --enable-rasa --rasa-url http://localhost:5005
```

### Set Confidence Threshold
```bash
python manage.py configure_ai_assistant --confidence-threshold 0.8
```

## Testing

### Run Navigation Tests
```bash
python test_navigation_functionality.py
```

### Test AI Response Generation
```bash
python test_ai_response.py
```

### Access Demo Page
Navigate to: `/ai-assistant/demo/navigation/`

## Example Interactions

### Employee Profile Requests
| User Input | AI Response | Navigation |
|------------|-------------|------------|
| "get sethu profile" | "Navigating to Sethu's profile." | `/employee/employee-view/1/` |
| "show john doe" | "Found multiple employees: John Doe, John Wilson. Showing search results." | `/employee/employee-view/?search=john` |
| "find jane smith" | "Navigating to Jane Smith's profile." | `/employee/employee-view/2/` |

### HR Operations
| User Input | AI Response | Navigation |
|------------|-------------|------------|
| "check leave balance" | "Navigating to your leave balance information." | `/leave/user-leave-request/` |
| "apply for leave" | "Navigating to the leave application form." | `/leave/user-request-create/` |
| "view attendance" | "Navigating to your attendance information." | `/attendance/attendance-view/` |

## Technical Implementation

### Key Files Modified
- `ai_assistant/rasa_integration.py` - Core navigation logic
- `ai_assistant/static/ai_assistant/js/chat.js` - Frontend navigation handling
- `ai_assistant/static/ai_assistant/css/chat.css` - Navigation UI styles
- `ai_assistant/tests.py` - Navigation test cases

### Navigation Flow
```
User Message → Intent Recognition → Name Extraction → Database Search → Navigation Decision → Page Redirect
```

### Response Format
```json
{
  "action": "navigate",
  "url": "/employee/employee-view/1/",
  "filters": {},
  "message": "Navigating to John Doe's profile.",
  "confidence": 0.95,
  "intent": "get_employee_profile"
}
```

## Troubleshooting

### Rasa Connection Issues
If you see "Failed to connect to Rasa server", the system will automatically fall back to mock responses. This is normal when Rasa is not running.

### Database Connection Issues
If employee search fails, the system will fall back to a general search page with the employee name as a filter.

### Navigation Not Working
1. Check browser console for JavaScript errors
2. Verify the navigation URLs are correct for your HRMS setup
3. Ensure user has proper permissions for the target pages

## Benefits

✅ **Improved User Experience**: Direct navigation instead of text responses
✅ **Intelligent Search**: Handles single/multiple/no matches gracefully
✅ **Visual Feedback**: Loading indicators and smooth transitions
✅ **Fallback Support**: Works even when Rasa server is unavailable
✅ **Comprehensive Testing**: Full test coverage with validation scripts

## Future Enhancements

- Voice navigation support
- Advanced filtering options
- Integration with more HRMS modules
- Personalized navigation preferences
- Analytics and usage tracking
