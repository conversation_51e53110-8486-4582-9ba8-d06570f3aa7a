<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Floating AI Assistant Widget Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    {% load static %}
    <link rel="stylesheet" href="{% static 'ai_assistant/css/floating-widget.css' %}">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .demo-container {
            padding: 50px 0;
            color: white;
        }
        
        .demo-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .feature-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .demo-section {
            margin-top: 40px;
        }
        
        .code-block {
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin-top: 20px;
        }
        
        .widget-indicator {
            position: fixed;
            bottom: 100px;
            right: 30px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 14px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            animation: bounce 2s infinite;
            z-index: 9998;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
        
        .btn-demo {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .btn-demo:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="demo-card">
                        <div class="text-center">
                            <h1 class="display-4 mb-3">🤖 Floating AI Assistant</h1>
                            <p class="lead">Experience the next-generation HR assistant with voice, text, and file support</p>
                        </div>
                        
                        <div class="feature-grid">
                            <div class="feature-card">
                                <div class="feature-icon">💬</div>
                                <h5>Text Chat</h5>
                                <p>Natural language conversations with intelligent responses and navigation</p>
                            </div>
                            
                            <div class="feature-card">
                                <div class="feature-icon">🎤</div>
                                <h5>Voice Input</h5>
                                <p>Speak naturally and get instant responses with speech recognition</p>
                            </div>
                            
                            <div class="feature-card">
                                <div class="feature-icon">📎</div>
                                <h5>File Upload</h5>
                                <p>Upload documents, images, and files for AI analysis and processing</p>
                            </div>
                            
                            <div class="feature-card">
                                <div class="feature-icon">🧭</div>
                                <h5>Smart Navigation</h5>
                                <p>Automatic redirection to relevant HR pages and employee profiles</p>
                            </div>
                        </div>
                        
                        <div class="demo-section">
                            <h3>Try These Commands:</h3>
                            <div class="text-center">
                                <button class="btn btn-demo" onclick="sendDemoMessage('get john doe profile')">
                                    👤 Get Employee Profile
                                </button>
                                <button class="btn btn-demo" onclick="sendDemoMessage('check my leave balance')">
                                    🏖️ Check Leave Balance
                                </button>
                                <button class="btn btn-demo" onclick="sendDemoMessage('view attendance report')">
                                    📊 View Attendance
                                </button>
                                <button class="btn btn-demo" onclick="sendDemoMessage('apply for vacation leave')">
                                    📝 Apply for Leave
                                </button>
                                <button class="btn btn-demo" onclick="sendDemoMessage('find employees in IT department')">
                                    🔍 Search Employees
                                </button>
                                <button class="btn btn-demo" onclick="sendDemoMessage('help me with payroll')">
                                    💰 Payroll Help
                                </button>
                            </div>
                        </div>
                        
                        <div class="demo-section">
                            <h3>Features:</h3>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="list-unstyled">
                                        <li>✅ Floating widget design</li>
                                        <li>✅ Text input with auto-resize</li>
                                        <li>✅ Voice recognition support</li>
                                        <li>✅ File upload capabilities</li>
                                        <li>✅ Smart navigation</li>
                                        <li>✅ Typing indicators</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="list-unstyled">
                                        <li>✅ Chat history persistence</li>
                                        <li>✅ Quick action buttons</li>
                                        <li>✅ Mobile responsive</li>
                                        <li>✅ Dark mode support</li>
                                        <li>✅ Accessibility features</li>
                                        <li>✅ Real-time responses</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="demo-section">
                            <h3>Integration Code:</h3>
                            <div class="code-block">
&lt;!-- Include in your base template --&gt;
{% templatetag openblock %} include 'ai_assistant/floating_widget.html' {% templatetag closeblock %}

&lt;!-- Or initialize manually --&gt;
&lt;script&gt;
const aiWidget = new FloatingAIWidget({
    apiUrl: '/ai-assistant/api/message/',
    uploadUrl: '/ai-assistant/api/upload/',
    enableVoice: true,
    enableFileUpload: true,
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedFileTypes: ['image/*', 'application/pdf', '.doc', '.docx']
});
&lt;/script&gt;
                            </div>
                        </div>
                        
                        <div class="text-center mt-4">
                            <p class="mb-0">
                                <strong>Look for the floating button in the bottom-right corner! 👉</strong>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Widget Indicator -->
    <div class="widget-indicator">
        Click the AI button! →
    </div>
    
    <!-- Include the floating widget -->
    {% include 'ai_assistant/floating_widget.html' %}
    
    <script>
        function sendDemoMessage(message) {
            if (window.aiWidget) {
                // Open widget if closed
                if (!window.aiWidget.isOpen) {
                    window.aiWidget.openWidget();
                }
                
                // Send the demo message
                setTimeout(() => {
                    window.aiWidget.textInput.value = message;
                    window.aiWidget.sendMessage();
                }, 500);
            }
        }
        
        // Hide indicator after widget is opened
        document.addEventListener('DOMContentLoaded', function() {
            const indicator = document.querySelector('.widget-indicator');
            
            // Check if widget is opened
            const checkWidget = setInterval(() => {
                if (window.aiWidget && window.aiWidget.isOpen) {
                    indicator.style.display = 'none';
                    clearInterval(checkWidget);
                }
            }, 1000);
            
            // Auto-hide after 10 seconds
            setTimeout(() => {
                indicator.style.opacity = '0';
                setTimeout(() => {
                    indicator.style.display = 'none';
                }, 500);
            }, 10000);
        });
    </script>
    
    {% csrf_token %}
</body>
</html>
